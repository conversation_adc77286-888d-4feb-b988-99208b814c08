window.translationOverlays = window.translationOverlays || {
  overlays: new Map(),
  loadingOverlays: new Map(),
  actionButtons: new Map(),
  scrollHandler: null,
  intersectionObserver: null,
  visibilityObserver: null, // 图片可见性监控器
  mutationObserver: null, // DOM变化监控器（用于实时检测图片切换）
  imageCache: new Map(),
  processingImages: new Set(),
  translationModeEnabled: false,
  visibilityCheckInterval: null, // 定期可见性检查定时器
  imageStateMap: new Map(), // 图片状态映射表（URL -> 可见性状态）
  lastVisibilityCheck: 0, // 上次可见性检查时间戳

  // Utility function to normalize image URL
  normalizeImageUrl: function(url) {
    if (!url) return url;
    return url;
  },

  // Initialize intersection observer for lazy processing
  initIntersectionObserver: function() {
    if (this.intersectionObserver) return;

    this.intersectionObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          const imageUrl = this.normalizeImageUrl(img.src);

          // Filter out small icons and decorative images
          if (!this.isMainContentImage(img)) {
            return;
          }

          // 检查是否应该处理这个图片
          const shouldProcess = this.shouldProcessImage(imageUrl);

          if (shouldProcess) {
            console.log('主要内容图片进入视口，准备处理:', imageUrl);

            // 触发处理事件
            if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
              window.flutter_inappwebview.callHandler('onImageVisible', imageUrl);
            }
          } else {
            console.log('图片不需要处理，跳过:', imageUrl, {
              cached: this.imageCache.has(imageUrl),
              processing: this.processingImages.has(imageUrl)
            });
          }
        }
      });
    }, {
      root: null,
      rootMargin: '50px',
      threshold: 0.1
    });

    // Observe only main content images
    document.querySelectorAll('img').forEach(img => {
      if (this.isMainContentImage(img)) {
        this.intersectionObserver.observe(img);
        console.log('Observing main content image:', img.src);
      }
    });
  },

  // 检查图片是否为主要内容（非图标/装饰性图片）
  isMainContentImage: function(img) {
    let url = this.normalizeImageUrl(img.src);
    const alt = (img.alt || '').toLowerCase();

    // 跳过没有src或data URL的图片
    if (!url || url.startsWith('data:')) {
      return false;
    }

    url = url.toLowerCase();

    // 跳过常见的图标/装饰性关键词
    const skipKeywords = [
      'logo', 'icon', 'favicon', 'avatar', 'thumb', 'thumbnail',
      'button', 'arrow', 'star', 'heart', 'like', 'share',
      'comment', 'menu', 'header', 'footer', 'banner',
      'advertisement', 'sponsor', 'widget', 'brand',
      'interface', 'control', 'social', 'badge'
    ];

    for (const keyword of skipKeywords) {
      if (url.includes(keyword) || alt.includes(keyword)) {
        return false;
      }
    }

    // 检查图片尺寸
    const width = img.naturalWidth || img.width || 0;
    const height = img.naturalHeight || img.height || 0;

    // 跳过太小的图片（可能是图标）
    if (width < 100 || height < 100) {
      return false;
    }

    return true;
  },

  // 检查图片是否在视觉上可见（考虑CSS样式）
  isImageVisuallyVisible: function(img) {
    if (!img || !img.parentNode) {
      return false;
    }

    // 检查图片本身的可见性
    const imgStyle = window.getComputedStyle(img);
    if (imgStyle.display === 'none' ||
        imgStyle.visibility === 'hidden' ||
        imgStyle.opacity === '0') {
      return false;
    }

    // 检查父元素的可见性（递归向上检查）
    let parent = img.parentElement;
    while (parent && parent !== document.body) {
      const parentStyle = window.getComputedStyle(parent);
      if (parentStyle.display === 'none' ||
          parentStyle.visibility === 'hidden' ||
          parentStyle.opacity === '0') {
        return false;
      }
      parent = parent.parentElement;
    }

    // 检查图片是否在视口内（基本检查）
    const rect = img.getBoundingClientRect();
    const isInViewport = rect.width > 0 && rect.height > 0;

    return isInViewport;
  },

  // 判断图片是否应该被处理（综合检查）
  shouldProcessImage: function(imageUrl) {
    // 基本检查：缓存状态
    if (this.imageCache.has(imageUrl)) {
      return false;
    }

    // 检查是否正在处理（但允许一定的容错）
    if (this.processingImages.has(imageUrl)) {
      // 检查处理时间，如果超过30秒则认为可能卡住了，允许重新处理
      const now = Date.now();
      const imageState = this.imageStateMap.get(imageUrl);
      if (imageState && imageState.lastChecked && (now - imageState.lastChecked > 30000)) {
        console.log('图片处理可能卡住，允许重新处理:', imageUrl);
        this.processingImages.delete(imageUrl);
        return true;
      }
      return false;
    }

    // 检查图片可见性状态
    const imageState = this.imageStateMap.get(imageUrl);
    if (imageState && !imageState.isVisible) {
      return false;
    }

    return true;
  },

  // 初始化图片可见性监控（改进版：实时检测 + 定期检查）
  initVisibilityMonitoring: function() {
    console.log('正在启动图片可见性监控系统...');

    // 1. 停止现有监控
    this.stopVisibilityMonitoring();

    // 2. 初始化图片状态映射
    this.initImageStateMap();

    // 3. 启动MutationObserver实时监控
    this.initMutationObserver();

    // 4. 启动定期检查作为备用机制（降低频率）
    this.visibilityCheckInterval = setInterval(() => {
      this.performPeriodicVisibilityCheck();
    }, 5000); // 每5秒检查一次作为备用

    console.log('图片可见性监控系统已启动（实时检测 + 定期备用检查）');
  },

  // 初始化图片状态映射表
  initImageStateMap: function() {
    this.imageStateMap.clear();

    // 记录当前所有图片的可见性状态
    const images = document.querySelectorAll('img');
    images.forEach(img => {
      if (this.isMainContentImage(img)) {
        const imageUrl = this.normalizeImageUrl(img.src);
        const isVisible = this.isImageVisuallyVisible(img);
        this.imageStateMap.set(imageUrl, {
          isVisible: isVisible,
          lastChecked: Date.now(),
          hasOverlay: this.hasImageOverlays(imageUrl),
          element: img
        });
      }
    });

    console.log(`已初始化 ${this.imageStateMap.size} 个图片的状态映射`);
  },

  // 初始化DOM变化监控器（实时检测图片切换）
  initMutationObserver: function() {
    if (this.mutationObserver) {
      this.mutationObserver.disconnect();
    }

    this.mutationObserver = new MutationObserver((mutations) => {
      this.handleDOMChanges(mutations);
    });

    // 监控配置：关注属性变化（特别是style属性）和子节点变化
    const config = {
      attributes: true,
      attributeFilter: ['style', 'class', 'hidden'], // 监控样式和类名变化
      childList: true, // 监控子节点添加/删除
      subtree: true, // 监控整个子树
      attributeOldValue: true // 保留旧属性值用于比较
    };

    this.mutationObserver.observe(document.body, config);
    console.log('DOM变化监控器已启动，监控图片切换事件');
  },

  // 处理DOM变化事件
  handleDOMChanges: function(mutations) {
    const now = Date.now();

    // 防抖：避免过于频繁的检查
    if (now - this.lastVisibilityCheck < 100) {
      return;
    }
    this.lastVisibilityCheck = now;

    let hasImageChanges = false;
    const changedImages = new Set();

    mutations.forEach(mutation => {
      // 处理属性变化（主要是style变化）
      if (mutation.type === 'attributes') {
        const target = mutation.target;

        // 检查是否是图片或包含图片的元素
        if (target.tagName === 'IMG' && this.isMainContentImage(target)) {
          changedImages.add(target);
          hasImageChanges = true;
        } else {
          // 检查子元素中的图片
          const childImages = target.querySelectorAll('img');
          childImages.forEach(img => {
            if (this.isMainContentImage(img)) {
              changedImages.add(img);
              hasImageChanges = true;
            }
          });
        }
      }

      // 处理节点添加/删除
      else if (mutation.type === 'childList') {
        // 检查新添加的图片
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            if (node.tagName === 'IMG' && this.isMainContentImage(node)) {
              changedImages.add(node);
              hasImageChanges = true;
            } else {
              const childImages = node.querySelectorAll('img');
              childImages.forEach(img => {
                if (this.isMainContentImage(img)) {
                  changedImages.add(img);
                  hasImageChanges = true;
                }
              });
            }
          }
        });
      }
    });

    // 如果检测到图片相关变化，立即检查可见性
    if (hasImageChanges) {
      console.log(`检测到 ${changedImages.size} 个图片发生变化，立即检查可见性`);
      this.checkChangedImages(Array.from(changedImages));
    }
  },

  // 检查发生变化的图片
  checkChangedImages: function(changedImages) {
    const imagesToClean = [];
    const imagesToUpdate = [];

    changedImages.forEach(img => {
      const imageUrl = this.normalizeImageUrl(img.src);
      const currentVisibility = this.isImageVisuallyVisible(img);
      const previousState = this.imageStateMap.get(imageUrl);

      if (previousState) {
        // 检查可见性是否发生变化
        if (previousState.isVisible !== currentVisibility) {
          console.log(`图片可见性发生变化: ${imageUrl}, ${previousState.isVisible} -> ${currentVisibility}`);

          if (!currentVisibility && previousState.hasOverlay) {
            // 图片从可见变为不可见，且有overlay，需要清理
            imagesToClean.push(imageUrl);
          } else if (currentVisibility && !previousState.isVisible) {
            // 图片从不可见变为可见，更新状态
            imagesToUpdate.push(imageUrl);
          }
        }
      } else {
        // 新图片，添加到状态映射
        this.imageStateMap.set(imageUrl, {
          isVisible: currentVisibility,
          lastChecked: Date.now(),
          hasOverlay: this.hasImageOverlays(imageUrl),
          element: img
        });

        if (currentVisibility) {
          console.log(`发现新的可见图片: ${imageUrl}`);
        }
      }

      // 更新状态映射
      if (previousState) {
        previousState.isVisible = currentVisibility;
        previousState.lastChecked = Date.now();
        previousState.element = img;
      }
    });

    // 执行清理操作
    imagesToClean.forEach(imageUrl => {
      console.log('实时检测到图片隐藏，立即清理:', imageUrl);
      this.cleanupImageCompletely(imageUrl);

      // 更新状态映射中的overlay状态
      const state = this.imageStateMap.get(imageUrl);
      if (state) {
        state.hasOverlay = false;
      }
    });

    // 通知新图片可见（使用统一的检查逻辑）
    imagesToUpdate.forEach(imageUrl => {
      console.log('检测到图片重新可见:', imageUrl);

      // 使用统一的处理检查逻辑
      if (this.shouldProcessImage(imageUrl)) {
        console.log('图片需要处理，通知Flutter端:', imageUrl);
        if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
          window.flutter_inappwebview.callHandler('onImageVisible', imageUrl);
        }
      } else {
        console.log('图片不需要处理，跳过通知:', imageUrl);
      }
    });

    if (imagesToClean.length > 0 || imagesToUpdate.length > 0) {
      console.log(`实时检测完成: 清理了 ${imagesToClean.length} 个隐藏图片, 发现 ${imagesToUpdate.length} 个新可见图片`);
    }
  },

  // 定期可见性检查（作为备用机制）
  performPeriodicVisibilityCheck: function() {
    console.log('执行定期可见性检查（备用机制）');

    const imagesToClean = [];
    const now = Date.now();

    // 检查状态映射中的所有图片
    this.imageStateMap.forEach((state, imageUrl) => {
      // 跳过最近已检查的图片
      if (now - state.lastChecked < 3000) {
        return;
      }

      const currentVisibility = this.isImageStillVisible(imageUrl);

      if (state.isVisible !== currentVisibility) {
        console.log(`定期检查发现可见性变化: ${imageUrl}, ${state.isVisible} -> ${currentVisibility}`);

        if (!currentVisibility && state.hasOverlay) {
          imagesToClean.push(imageUrl);
        }

        // 更新状态
        state.isVisible = currentVisibility;
        state.lastChecked = now;
        if (!currentVisibility) {
          state.hasOverlay = false;
        }
      }
    });

    // 执行清理
    imagesToClean.forEach(imageUrl => {
      console.log('定期检查发现隐藏图片，执行清理:', imageUrl);
      this.cleanupImageCompletely(imageUrl);
    });

    // 同时执行传统的全面检查（降低频率）
    if (now % 15000 < 5000) { // 每15秒执行一次全面检查
      this.checkAndCleanHiddenImages();
    }
  },

  // 停止图片可见性监控
  stopVisibilityMonitoring: function() {
    console.log('正在停止图片可见性监控系统...');

    // 停止定期检查
    if (this.visibilityCheckInterval) {
      clearInterval(this.visibilityCheckInterval);
      this.visibilityCheckInterval = null;
    }

    // 停止DOM变化监控
    if (this.mutationObserver) {
      this.mutationObserver.disconnect();
      this.mutationObserver = null;
    }

    // 清理状态映射
    this.imageStateMap.clear();

    console.log('图片可见性监控系统已完全停止');
  },

  // 检查并清理隐藏图片的相关内容
  checkAndCleanHiddenImages: function() {
    const imagesToClean = [];

    // 检查所有有overlay的图片
    this.overlays.forEach((overlayData, overlayId) => {
      const imageUrl = overlayData.imageUrl;
      if (imageUrl && !this.isImageStillVisible(imageUrl)) {
        imagesToClean.push(imageUrl);
      }
    });

    // 检查所有有加载指示器的图片
    this.loadingOverlays.forEach((loadingData, imageUrl) => {
      if (!this.isImageStillVisible(imageUrl)) {
        imagesToClean.push(imageUrl);
      }
    });

    // 检查所有有操作按钮的图片
    this.actionButtons.forEach((button, imageUrl) => {
      if (!this.isImageStillVisible(imageUrl)) {
        imagesToClean.push(imageUrl);
      }
    });

    // 去重并清理
    const uniqueImagesToClean = [...new Set(imagesToClean)];
    uniqueImagesToClean.forEach(imageUrl => {
      console.log('检测到隐藏图片，正在清理相关内容:', imageUrl);
      this.cleanupImageCompletely(imageUrl);
    });

    if (uniqueImagesToClean.length > 0) {
      console.log(`已清理 ${uniqueImagesToClean.length} 个隐藏图片的相关内容`);
    }
  },

  // 检查指定URL的图片是否仍然可见
  isImageStillVisible: function(imageUrl) {
    const images = document.querySelectorAll('img');
    for (const img of images) {
      if (this.normalizeImageUrl(img.src) === imageUrl) {
        return this.isImageVisuallyVisible(img);
      }
    }
    return false; // 图片不存在或不可见
  },

  // 完全清理图片的所有相关内容
  cleanupImageCompletely: function(imageUrl) {
    try {
      // 1. 移除翻译overlay
      const overlaysRemoved = this.removeImageOverlays(imageUrl);

      // 2. 移除加载指示器
      this.hideLoadingIndicator(imageUrl);

      // 3. 移除缓存指示器
      this.hideCacheIndicator(imageUrl);

      // 4. 移除操作按钮
      this.removeActionButton(imageUrl);

      // 5. 清理缓存状态
      this.imageCache.delete(imageUrl);
      this.processingImages.delete(imageUrl);

      console.log(`已完全清理图片相关内容: ${imageUrl}, 移除了 ${overlaysRemoved ? '翻译overlay' : '无overlay'}`);

      // 6. 通知Flutter端图片已隐藏
      if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
        window.flutter_inappwebview.callHandler('onImageHidden', imageUrl);
      }

      return true;
    } catch (error) {
      console.error('清理图片内容时发生错误:', imageUrl, error);
      return false;
    }
  },

  // Show loading indicator on image
  showLoadingIndicator: function(imageUrl, loadingText) {
    const images = document.querySelectorAll('img');
    let targetImage = null;

    for (let img of images) {
      const imgSrc = this.normalizeImageUrl(img.src);

      if (imgSrc === imageUrl || imgSrc.includes(imageUrl.split('/').pop())) {
        targetImage = img;
        break;
      }
    }

    if (!targetImage) return;

    this.processingImages.add(imageUrl);

    const imageRect = targetImage.getBoundingClientRect();
    const scrollX = window.scrollX;
    const scrollY = window.scrollY;

    const loadingOverlay = document.createElement('div');
    loadingOverlay.id = 'loading-overlay-' + Date.now();
    loadingOverlay.className = 'translation-loading-overlay';

    // Create spinner and text
    const spinner = document.createElement('div');
    spinner.className = 'translation-loading-spinner';

    const text = document.createElement('div');
    text.textContent = loadingText || 'Translating...';

    loadingOverlay.appendChild(spinner);
    loadingOverlay.appendChild(text);

    // Cover the entire image with semi-transparent overlay - ensure proper positioning
    const imageLeft = imageRect.left + scrollX;
    const imageTop = imageRect.top + scrollY;
    const imageWidth = Math.max(imageRect.width, 120); // Minimum width
    const imageHeight = Math.max(imageRect.height, 80); // Minimum height

    loadingOverlay.style.position = 'absolute';
    loadingOverlay.style.left = imageLeft + 'px';
    loadingOverlay.style.top = imageTop + 'px';
    loadingOverlay.style.width = imageWidth + 'px';
    loadingOverlay.style.height = imageHeight + 'px';
    loadingOverlay.style.zIndex = '10001';

    // Ensure the overlay is visible and properly styled
    loadingOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    loadingOverlay.style.color = 'white';
    loadingOverlay.style.display = 'flex';
    loadingOverlay.style.alignItems = 'center';
    loadingOverlay.style.justifyContent = 'center';
    loadingOverlay.style.flexDirection = 'column';
    loadingOverlay.style.borderRadius = '8px';
    loadingOverlay.style.border = '2px solid rgba(255, 255, 255, 0.3)';
    loadingOverlay.style.opacity = '1';
    loadingOverlay.style.visibility = 'visible';

    document.body.appendChild(loadingOverlay);
    this.loadingOverlays.set(imageUrl, loadingOverlay);

    console.log('Loading overlay created:', {
      id: loadingOverlay.id,
      position: { left: loadingOverlay.style.left, top: loadingOverlay.style.top },
      size: { width: loadingOverlay.style.width, height: loadingOverlay.style.height },
      zIndex: loadingOverlay.style.zIndex,
      imageUrl: imageUrl,
      imageRect: { left: imageRect.left, top: imageRect.top, width: imageRect.width, height: imageRect.height },
      scroll: { x: scrollX, y: scrollY },
      finalPosition: { left: imageLeft, top: imageTop, width: imageWidth, height: imageHeight }
    });

    // Set a timeout to auto-hide loading indicator after 30 seconds
    setTimeout(() => {
      if (this.loadingOverlays.has(imageUrl)) {
        console.log('Auto-hiding loading indicator after timeout for:', imageUrl);
        this.hideLoadingIndicator(imageUrl);
      }
    }, 30000); // 30 seconds timeout

    console.log('Loading indicator shown for:', imageUrl);
  },

  // Hide loading indicator
  hideLoadingIndicator: function(imageUrl) {
    console.log('Attempting to hide loading indicator for:', imageUrl);

    // Try exact match first
    let loadingOverlay = this.loadingOverlays.get(imageUrl);
    if (loadingOverlay) {
      loadingOverlay.remove();
      this.loadingOverlays.delete(imageUrl);
      this.processingImages.delete(imageUrl);
      console.log('Loading indicator hidden for:', imageUrl);
      return;
    }

    // Try partial match for blob URLs
    const images = document.querySelectorAll('img');
    for (let img of images) {
      const imgSrc = this.normalizeImageUrl(img.src);
      if (imgSrc === imageUrl || imgSrc.includes(imageUrl.split('/').pop())) {
        // Find and remove loading overlay for this image
        this.loadingOverlays.forEach((overlay, key) => {
          if (key === imageUrl || key.includes(imageUrl.split('/').pop())) {
            overlay.remove();
            this.loadingOverlays.delete(key);
            this.processingImages.delete(key);
            console.log('Loading indicator hidden for partial match:', key);
          }
        });
        break;
      }
    }
  },

  // Force hide all loading indicators (cleanup function)
  hideAllLoadingIndicators: function() {
    console.log('Hiding all loading indicators, count:', this.loadingOverlays.size);
    this.loadingOverlays.forEach((overlay, imageUrl) => {
      console.log('Removing loading overlay for:', imageUrl);
      overlay.remove();
      this.processingImages.delete(imageUrl);
    });
    this.loadingOverlays.clear();
  },

  // Debug loading indicators status
  debugLoadingIndicators: function() {
    const loadingOverlays = document.querySelectorAll('.translation-loading-overlay');
    const debugInfo = {
      mapSize: this.loadingOverlays.size,
      domElements: loadingOverlays.length,
      processingImages: Array.from(this.processingImages),
      overlayDetails: Array.from(loadingOverlays).map((overlay, index) => {
        const rect = overlay.getBoundingClientRect();
        return {
          id: overlay.id,
          className: overlay.className,
          position: {
            left: overlay.style.left,
            top: overlay.style.top,
            width: overlay.style.width,
            height: overlay.style.height
          },
          computed: {
            left: rect.left,
            top: rect.top,
            width: rect.width,
            height: rect.height
          },
          visible: rect.width > 0 && rect.height > 0,
          zIndex: overlay.style.zIndex,
          opacity: window.getComputedStyle(overlay).opacity
        };
      })
    };
    console.log('Loading indicators debug:', debugInfo);
    return debugInfo;
  },

  // Create and display overlays for translated text elements
  createImageOverlay: function(id, text, imageUrl, x, y, width, height, fontSize) {
    console.log('=== Creating overlay ===');
    console.log('ID:', id);
    console.log('Text:', text);
    console.log('ImageURL:', imageUrl);
    console.log('Position:', x, y, width, height);
    console.log('Current overlays count:', this.overlays.size);

    // Check if overlay with this ID already exists in Map
    if (this.overlays.has(id)) {
      console.log('WARNING: Overlay with ID', id, 'already exists in Map, skipping creation');
      console.log('Existing overlay:', this.overlays.get(id));
      return this.overlays.get(id).element;
    }

    // Also check if DOM element with this ID already exists
    const existingDomElement = document.getElementById('translation-overlay-' + id);
    if (existingDomElement) {
      console.log('WARNING: DOM element with ID translation-overlay-' + id + ' already exists, removing it');
      existingDomElement.remove();
    }

    // Hide loading indicator for this specific image only (not all)
    console.log('Hiding loading indicator for image:', imageUrl);
    this.hideLoadingIndicator(imageUrl);

    // Find the image element
    const images = document.querySelectorAll('img');
    let targetImage = null;

    for (let img of images) {
      console.log('Checking image:', img.src);
      const imgSrc = this.normalizeImageUrl(img.src);

      if (imgSrc === imageUrl || imgSrc.includes(imageUrl.split('/').pop())) {
        targetImage = img;
        console.log('Found target image:', img.src);
        break;
      }
    }

    if (!targetImage) {
      console.warn('Target image not found for overlay:', imageUrl);
      console.log('Available images:', Array.from(images).map(img => img.src));
      // Still hide loading indicator even if image not found
      this.hideLoadingIndicator(imageUrl);
      // Fallback to absolute positioning
      return this.createOverlay(id, text, imageUrl, x, y, width, height, fontSize);
    }

    // Wait for image to load if necessary
    if (!targetImage.complete || targetImage.naturalWidth === 0) {
      console.log('Image not loaded yet, waiting...');
      targetImage.onload = () => {
        this.createImageOverlay(id, text, imageUrl, x, y, width, height, fontSize);
      };
      // Set a timeout to hide loading indicator if image fails to load
      setTimeout(() => {
        this.hideLoadingIndicator(imageUrl);
      }, 10000); // 10 second timeout
      return null;
    }

    // Get image position and size
    const imageRect = targetImage.getBoundingClientRect();
    const scrollX = window.scrollX;
    const scrollY = window.scrollY;

    console.log('Image rect:', imageRect);
    console.log('Natural size:', targetImage.naturalWidth, targetImage.naturalHeight);
    console.log('Scroll position:', scrollX, scrollY);

    // Calculate scale factors for coordinate transformation
    const scaleX = imageRect.width / targetImage.naturalWidth;
    const scaleY = imageRect.height / targetImage.naturalHeight;

    console.log('Scale factors:', { scaleX, scaleY });
    console.log('Image dimensions:', {
      natural: { width: targetImage.naturalWidth, height: targetImage.naturalHeight },
      displayed: { width: imageRect.width, height: imageRect.height }
    });

    // OCR coordinates are relative to the natural image size
    // Transform them to the displayed image coordinates
    const scaledX = x * scaleX;
    const scaledY = y * scaleY;

    // Calculate absolute position on the page
    // Add image's position on page plus the scaled OCR coordinates
    const absoluteX = imageRect.left + scrollX + scaledX;
    const absoluteY = imageRect.top + scrollY + scaledY;

    console.log('Position calculations:', {
      ocrCoordinates: { x, y },
      scaledCoordinates: { x: scaledX, y: scaledY },
      imagePosition: { left: imageRect.left, top: imageRect.top },
      scroll: { x: scrollX, y: scrollY },
      finalAbsolute: { x: absoluteX, y: absoluteY }
    });

    // Ensure overlay stays within reasonable bounds (but don't be too restrictive)
    const finalX = Math.max(0, absoluteX);
    const finalY = Math.max(0, absoluteY);

    console.log('Final position after bounds check:', { x: finalX, y: finalY });

    const overlay = document.createElement('div');
    overlay.id = 'translation-overlay-' + id;
    overlay.className = 'translation-overlay';
    overlay.textContent = text;
    overlay.style.left = finalX + 'px';
    overlay.style.top = finalY + 'px';
    overlay.style.fontSize = fontSize + 'px';

    // Set overlay dimensions - use scaled dimensions but ensure minimum visibility
    const overlayWidth = Math.max(width * scaleX, 80); // Minimum width of 80px
    const overlayHeight = Math.max(height * scaleY, fontSize * 1.5); // Minimum height based on font size

    overlay.style.width = overlayWidth + 'px';
    overlay.style.height = overlayHeight + 'px';
    overlay.style.maxWidth = 'none';
    overlay.style.minWidth = '80px';
    overlay.style.minHeight = (fontSize * 1.2) + 'px';
    overlay.style.zIndex = '10000';

    // Ensure proper styling for visibility and readability
    overlay.style.position = 'absolute';
    overlay.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
    overlay.style.color = '#333';
    overlay.style.border = '1px solid rgba(0,0,0,0.1)';
    overlay.style.borderRadius = '4px';
    overlay.style.padding = '4px 6px';
    overlay.style.boxShadow = '0 2px 8px rgba(0,0,0,0.2)';
    overlay.style.opacity = '1';
    overlay.style.visibility = 'visible';
    overlay.style.display = 'flex';
    overlay.style.alignItems = 'center';
    overlay.style.justifyContent = 'center';
    overlay.style.textAlign = 'center';
    overlay.style.fontWeight = '500';

    console.log('Overlay dimensions set:', {
      width: overlayWidth,
      height: overlayHeight,
      originalOcrWidth: width,
      originalOcrHeight: height,
      scaleX: scaleX,
      scaleY: scaleY
    });

    // Ensure text wraps properly within the overlay area (use modern CSS)
    overlay.style.overflowWrap = 'break-word';
    overlay.style.whiteSpace = 'normal';
    overlay.style.overflow = 'hidden';

    // Add the overlay to the DOM first
    document.body.appendChild(overlay);

    // Store overlay data with corrected coordinates
    this.overlays.set(id, {
      element: overlay,
      targetImage: targetImage,
      originalX: x, // Store original OCR coordinates
      originalY: y,
      scrollX: scrollX,
      scrollY: scrollY,
      imageUrl: imageUrl
    });

    // Mark image as processed in cache
    this.imageCache.set(imageUrl, true);

    console.log('=== Overlay creation completed ===');
    console.log('Overlay ID:', id);
    console.log('Overlay element added to DOM:', overlay);
    console.log('Overlay position in DOM:', {
      left: overlay.style.left,
      top: overlay.style.top,
      width: overlay.style.width,
      height: overlay.style.height,
      zIndex: overlay.style.zIndex,
      opacity: overlay.style.opacity
    });
    console.log('Total overlays now:', this.overlays.size);
    console.log('All overlay IDs:', Array.from(this.overlays.keys()));

    // Update action button state to completed after overlay is created
    this.updateActionButtonState(imageUrl, 'completed');

    // Fade in animation
    setTimeout(() => {
      overlay.classList.add('fade-in');
      console.log('Fade in animation applied for overlay:', id);
    }, 10);

    return overlay;
  },

  createOverlay: function(id, text, imageUrl, x, y, width, height, fontSize) {
    // Check if overlay with this ID already exists
    if (this.overlays.has(id)) {
      console.log('Overlay with ID', id, 'already exists, skipping creation');
      return this.overlays.get(id).element;
    }

    // Fallback to absolute positioning
    const overlay = document.createElement('div');
    overlay.id = 'translation-overlay-' + id;
    overlay.className = 'translation-overlay';
    overlay.textContent = text;
    overlay.style.left = x + 'px';
    overlay.style.top = y + 'px';
    overlay.style.fontSize = fontSize + 'px';

    // Set dimensions to match OCR detected area
    const overlayWidth = Math.max(width, 100); // Minimum width of 100px
    const overlayHeight = Math.max(height, fontSize * 1.2); // Minimum height based on font size

    overlay.style.width = overlayWidth + 'px';
    overlay.style.height = overlayHeight + 'px';
    overlay.style.minWidth = Math.min(overlayWidth, 100) + 'px';
    overlay.style.maxWidth = 'none';

    // Apply consistent styling
    overlay.style.position = 'absolute';
    overlay.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
    overlay.style.color = '#333';
    overlay.style.border = '1px solid rgba(0,0,0,0.1)';
    overlay.style.borderRadius = '4px';
    overlay.style.padding = '4px 6px';
    overlay.style.boxShadow = '0 2px 8px rgba(0,0,0,0.2)';
    overlay.style.zIndex = '10000';
    overlay.style.opacity = '1';
    overlay.style.visibility = 'visible';
    overlay.style.display = 'flex';
    overlay.style.alignItems = 'center';
    overlay.style.justifyContent = 'center';
    overlay.style.textAlign = 'center';
    overlay.style.fontWeight = '500';

    // Ensure text wraps properly within the overlay area
    overlay.style.overflowWrap = 'break-word';
    overlay.style.whiteSpace = 'normal';
    overlay.style.overflow = 'hidden';

    document.body.appendChild(overlay);
    this.overlays.set(id, {
      element: overlay,
      originalX: x,
      originalY: y,
      scrollX: window.scrollX,
      scrollY: window.scrollY,
      imageUrl: imageUrl
    });

    // Update action button state to completed
    this.updateActionButtonState(imageUrl, 'completed');

    // Fade in animation
    setTimeout(() => overlay.classList.add('fade-in'), 10);

    return overlay;
  },
  
  removeOverlay: function(id) {
    const overlayData = this.overlays.get(id);
    if (overlayData) {
      overlayData.element.classList.add('fade-out');
      setTimeout(() => {
        if (overlayData.element.parentNode) {
          overlayData.element.parentNode.removeChild(overlayData.element);
        }
        this.overlays.delete(id);
      }, 200);
    }
  },
  
  removeAllOverlays: function() {
    // Collect all image URLs that have overlays before removing them
    const imageUrlsWithOverlays = new Set();
    this.overlays.forEach((overlayData, id) => {
      if (overlayData.imageUrl) {
        imageUrlsWithOverlays.add(overlayData.imageUrl);
      }
      this.removeOverlay(id);
    });
    this.overlays.clear();

    // Reset action button states for images that had overlays
    imageUrlsWithOverlays.forEach(imageUrl => {
      this.updateActionButtonState(imageUrl, 'ready');
    });

    console.log('Removed all overlays and reset', imageUrlsWithOverlays.size, 'action button states');
  },
  
  updateOverlayPositions: function() {
    const currentScrollX = window.scrollX;
    const currentScrollY = window.scrollY;

    this.overlays.forEach((overlayData) => {
      if (overlayData.targetImage) {
        // Image-relative positioning with corrected coordinate transformation
        const imageRect = overlayData.targetImage.getBoundingClientRect();
        const scaleX = imageRect.width / overlayData.targetImage.naturalWidth;
        const scaleY = imageRect.height / overlayData.targetImage.naturalHeight;

        // Transform OCR coordinates to current display coordinates
        const scaledX = overlayData.originalX * scaleX;
        const scaledY = overlayData.originalY * scaleY;

        const absoluteX = imageRect.left + currentScrollX + scaledX;
        const absoluteY = imageRect.top + currentScrollY + scaledY;

        overlayData.element.style.left = Math.max(0, absoluteX) + 'px';
        overlayData.element.style.top = Math.max(0, absoluteY) + 'px';
      } else {
        // Fallback to absolute positioning
        const deltaX = currentScrollX - overlayData.scrollX;
        const deltaY = currentScrollY - overlayData.scrollY;

        overlayData.element.style.left = (overlayData.originalX - deltaX) + 'px';
        overlayData.element.style.top = (overlayData.originalY - deltaY) + 'px';
      }
    });
  },
  
  setupScrollHandler: function() {
    if (this.scrollHandler) return;
    
    this.scrollHandler = () => {
      this.updateOverlayPositions();
    };
    
    window.addEventListener('scroll', this.scrollHandler, { passive: true });
    window.addEventListener('resize', this.scrollHandler, { passive: true });
  },
  
  removeScrollHandler: function() {
    if (this.scrollHandler) {
      window.removeEventListener('scroll', this.scrollHandler);
      window.removeEventListener('resize', this.scrollHandler);
      this.scrollHandler = null;
    }
  },

  // Initialize the system
  initialize: function() {
    this.initIntersectionObserver();
    this.setupScrollHandler();
    console.log('Translation overlay system initialized');
  },

  // Clear cache for specific image
  clearImageCache: function(imageUrl) {
    this.imageCache.delete(imageUrl);
    console.log('Cache cleared for:', imageUrl);
  },

  // Clear all cache
  clearAllCache: function() {
    this.imageCache.clear();
    console.log('All cache cleared');
  },

  // Check if image is cached
  isImageCached: function(imageUrl) {
    return this.imageCache.has(imageUrl);
  },

  // Get processing status
  isImageProcessing: function(imageUrl) {
    return this.processingImages.has(imageUrl);
  },

  // Check if image has translation overlays
  hasImageOverlays: function(imageUrl) {
    let hasOverlays = false;
    this.overlays.forEach((overlayData, id) => {
      if (overlayData.imageUrl === imageUrl) {
        hasOverlays = true;
      }
    });
    return hasOverlays;
  },

  // 移除指定图片的所有overlay（改进版，更彻底的清理）
  removeImageOverlays: function(imageUrl) {
    const overlaysToRemove = [];
    this.overlays.forEach((overlayData, id) => {
      if (overlayData.imageUrl === imageUrl) {
        overlaysToRemove.push(id);
      }
    });

    overlaysToRemove.forEach(id => {
      this.removeOverlay(id);
    });

    // 同时清理相关的加载指示器和缓存指示器
    this.hideLoadingIndicator(imageUrl);
    this.hideCacheIndicator(imageUrl);

    // 更新操作按钮状态
    if (overlaysToRemove.length > 0) {
      this.updateActionButtonState(imageUrl, 'ready');
    }

    console.log('已移除图片的', overlaysToRemove.length, '个overlay:', imageUrl);
    return overlaysToRemove.length > 0;
  },

  // 移除指定图片的操作按钮
  removeActionButton: function(imageUrl) {
    const button = this.actionButtons.get(imageUrl);
    if (button && button.parentNode) {
      button.parentNode.removeChild(button);
      this.actionButtons.delete(imageUrl);
      console.log('已移除图片的操作按钮:', imageUrl);
      return true;
    }
    return false;
  },

  // Enable/disable translation mode
  setTranslationMode: function(enabled, buttonText) {
    this.translationModeEnabled = enabled;
    this.buttonText = buttonText || 'Translate this image';
    if (enabled) {
      this.showAllActionButtons();
    } else {
      this.hideAllActionButtons();
    }
    console.log('Translation mode:', enabled ? 'enabled' : 'disabled');
  },

  // Show action buttons on all eligible images
  showAllActionButtons: function() {
    const images = document.querySelectorAll('img');
    images.forEach(img => {
      if (this.isMainContentImage(img)) {
        this.showActionButton(img);
      }
    });
  },

  // Hide all action buttons
  hideAllActionButtons: function() {
    this.actionButtons.forEach((button, imageUrl) => {
      if (button && button.parentNode) {
        button.parentNode.removeChild(button);
      }
    });
    this.actionButtons.clear();
  },

  // Show action button on specific image
  showActionButton: function(img) {
    const imageUrl = this.normalizeImageUrl(img.src);

    // Don't show button if already exists or image is being processed
    if (this.actionButtons.has(imageUrl) || this.processingImages.has(imageUrl)) {
      return;
    }

    const button = document.createElement('button');
    button.className = 'translation-action-button';
    button.innerHTML = '🌐'; // Globe icon
    button.title = this.buttonText || 'Translate this image';

    // Position button relative to image
    const updateButtonPosition = () => {
      const rect = img.getBoundingClientRect();
      const scrollX = window.scrollX;
      const scrollY = window.scrollY;

      button.style.position = 'absolute';
      button.style.left = (rect.right + scrollX - 44) + 'px'; // 44 = button width + margin
      button.style.top = (rect.top + scrollY + 8) + 'px';
      button.style.zIndex = '10001';
    };

    // Initial positioning
    updateButtonPosition();

    // Handle click
    button.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();

      if (this.processingImages.has(imageUrl)) {
        return; // Already processing
      }

      // Check if image already has overlays (toggle functionality)
      if (this.hasImageOverlays(imageUrl)) {
        // Remove existing overlays
        const removed = this.removeImageOverlays(imageUrl);
        if (removed) {
          // Update button state to ready
          button.classList.remove('processing', 'completed');
          button.innerHTML = '🌐'; // Globe icon
          button.title = this.buttonText || 'Translate this image';
          console.log('Toggled OFF: Removed overlays for', imageUrl);
        }
      } else {
        // Create new translation
        // Update button state
        button.classList.add('processing');
        button.innerHTML = '⏳'; // Hourglass icon
        button.title = 'Translating...';

        // Notify Flutter
        if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
          window.flutter_inappwebview.callHandler('onTranslateImageRequested', imageUrl);
          console.log('Toggled ON: Requesting translation for', imageUrl);
        }
      }
    });

    // Update position on scroll/resize
    const positionHandler = () => updateButtonPosition();
    window.addEventListener('scroll', positionHandler);
    window.addEventListener('resize', positionHandler);

    // Store cleanup function
    button._cleanup = () => {
      window.removeEventListener('scroll', positionHandler);
      window.removeEventListener('resize', positionHandler);
    };

    document.body.appendChild(button);
    this.actionButtons.set(imageUrl, button);

    console.log('Action button shown for:', imageUrl);
  },

  // Update action button state
  updateActionButtonState: function(imageUrl, state) {
    const button = this.actionButtons.get(imageUrl);
    if (!button) return;

    button.classList.remove('processing', 'completed');

    switch (state) {
      case 'processing':
        button.classList.add('processing');
        button.innerHTML = '⏳';
        button.title = 'Translating...';
        break;
      case 'completed':
        // Check if image actually has overlays
        if (this.hasImageOverlays(imageUrl)) {
          button.classList.add('completed');
          button.innerHTML = '✓';
          button.title = 'Translation completed - click to hide';
        } else {
          // No overlays, reset to ready state
          button.innerHTML = '🌐';
          button.title = this.buttonText || 'Translate this image';
        }
        break;
      case 'ready':
      default:
        button.innerHTML = '🌐';
        button.title = this.buttonText || 'Translate this image';
        break;
    }
  },

  // Remove action button for specific image
  removeActionButton: function(imageUrl) {
    const button = this.actionButtons.get(imageUrl);
    if (button) {
      if (button._cleanup) {
        button._cleanup();
      }
      if (button.parentNode) {
        button.parentNode.removeChild(button);
      }
      this.actionButtons.delete(imageUrl);
    }
  },

  // Show cache indicator for cached images
  showCacheIndicator: function(imageUrl) {
    // Find the image element
    const images = document.querySelectorAll('img');
    let targetImage = null;

    for (let img of images) {
      const imgSrc = this.normalizeImageUrl(img.src);

      if (imgSrc === imageUrl || imgSrc.includes(imageUrl.split('/').pop())) {
        targetImage = img;
        break;
      }
    }

    if (!targetImage) {
      console.warn('Target image not found for cache indicator:', imageUrl);
      return;
    }

    // Check if cache indicator already exists
    const existingIndicator = document.querySelector('[data-cache-indicator="' + imageUrl + '"]');
    if (existingIndicator) {
      return; // Already exists
    }

    const indicator = document.createElement('div');
    indicator.className = 'translation-cache-indicator';
    indicator.setAttribute('data-cache-indicator', imageUrl);
    indicator.innerHTML = '💾'; // Disk icon
    indicator.title = 'Translation cached - click to clear cache';

    // Position in top-left corner of image
    const imageRect = targetImage.getBoundingClientRect();
    const scrollX = window.scrollX;
    const scrollY = window.scrollY;

    indicator.style.position = 'absolute';
    indicator.style.left = (imageRect.left + scrollX + 8) + 'px';
    indicator.style.top = (imageRect.top + scrollY + 8) + 'px';
    indicator.style.zIndex = '10001';
    indicator.style.width = '24px';
    indicator.style.height = '24px';
    indicator.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    indicator.style.color = 'white';
    indicator.style.borderRadius = '12px';
    indicator.style.display = 'flex';
    indicator.style.alignItems = 'center';
    indicator.style.justifyContent = 'center';
    indicator.style.fontSize = '12px';
    indicator.style.cursor = 'pointer';
    indicator.style.zIndex = '10001';
    indicator.style.boxShadow = '0 2px 4px rgba(0,0,0,0.3)';

    // Handle click to clear cache
    indicator.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();

      // Notify Flutter to clear cache
      if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
        window.flutter_inappwebview.callHandler('onClearImageCache', imageUrl);
      }
    });

    // Update position on scroll/resize
    const updateIndicatorPosition = () => {
      const currentRect = targetImage.getBoundingClientRect();
      const currentScrollX = window.scrollX;
      const currentScrollY = window.scrollY;

      indicator.style.left = (currentRect.left + currentScrollX + 8) + 'px';
      indicator.style.top = (currentRect.top + currentScrollY + 8) + 'px';
    };

    const positionHandler = () => updateIndicatorPosition();
    window.addEventListener('scroll', positionHandler);
    window.addEventListener('resize', positionHandler);

    // Store cleanup function
    indicator._cleanup = () => {
      window.removeEventListener('scroll', positionHandler);
      window.removeEventListener('resize', positionHandler);
    };

    document.body.appendChild(indicator);
    console.log('Cache indicator shown for:', imageUrl);
  },

  // Hide cache indicator for specific image
  hideCacheIndicator: function(imageUrl) {
    const indicator = document.querySelector('[data-cache-indicator="' + imageUrl + '"]');
    if (indicator) {
      if (indicator._cleanup) {
        indicator._cleanup();
      }
      indicator.remove();
      console.log('Cache indicator hidden for:', imageUrl);
    }
  },

  // Hide all cache indicators
  hideAllCacheIndicators: function() {
    const indicators = document.querySelectorAll('.translation-cache-indicator');
    indicators.forEach(indicator => {
      if (indicator._cleanup) {
        indicator._cleanup();
      }
      indicator.remove();
    });
    console.log('All cache indicators hidden');
  }
}; 
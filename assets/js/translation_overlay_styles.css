.translation-overlay {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  padding: 2px 4px;
  color: #333;
  z-index: 10000;
  pointer-events: none;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: opacity 0.2s ease;
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300px;
  opacity: 1;
}

.translation-overlay.fade-in {
  opacity: 1;
}

.translation-overlay.fade-out {
  opacity: 0;
}

.translation-overlay-container {
  position: relative;
  z-index: 9999;
}

.translation-loading-overlay {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  z-index: 10001;
  pointer-events: none;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
}

.translation-loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.image-overlay-container {
  position: relative;
  display: inline-block;
}

.translation-action-button {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 36px;
  height: 36px;
  background-color: rgba(0, 123, 255, 0.9);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  z-index: 10001;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
  opacity: 0.8;
}

.translation-action-button:hover {
  opacity: 1;
  transform: scale(1.1);
  background-color: rgba(0, 123, 255, 1);
}

.translation-action-button.processing {
  background-color: rgba(255, 193, 7, 0.9);
  cursor: not-allowed;
}

.translation-action-button.completed {
  background-color: rgba(40, 167, 69, 0.9);
} 
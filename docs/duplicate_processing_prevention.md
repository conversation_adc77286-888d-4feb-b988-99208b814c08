# 防重复处理机制改进

## 问题分析

在MangaDX翻页时出现了重复处理同一图片的问题：

### 问题现象
```
flutter: MultiBrowserPage: Image became visible: blob:https://mangadx.org/6744b313-65e2-4cec-bcec-f3e5bc4ebc71
flutter: MultiBrowserPage: Processing translation request for: blob:https://mangadx.org/6744b313-65e2-4cec-bcec-f3e5bc4ebc71
flutter: MultiBrowserPage: Image became visible: blob:https://mangadx.org/6744b313-65e2-4cec-bcec-f3e5bc4ebc71
flutter: MultiBrowserPage: Processing translation request for: blob:https://mangadx.org/6744b313-65e2-4cec-bcec-f3e5bc4ebc71
```

### 根本原因
1. **图片可见性事件重复触发**：同一图片在短时间内多次触发`onImageVisible`事件
2. **缺乏防重复机制**：没有有效的机制防止同一图片被重复处理
3. **JavaScript和Dart端状态不同步**：两端的处理状态没有有效同步

## 解决方案

### 1. Dart端防重复机制

#### **MultiBrowserPage改进**
```dart
// 新增防重复处理机制
final Set<String> _processingImages = <String>{}; // 正在处理的图片URL集合
final Map<String, DateTime> _lastProcessTime = <String, DateTime>{}; // 图片最后处理时间
static const Duration _minProcessInterval = Duration(milliseconds: 1000); // 最小处理间隔
```

#### **_processVisibleImage方法增强**
```dart
Future<void> _processVisibleImage(String imageUrl, BuildContext context) async {
  // 检查翻译开关状态
  if (!_translationToggleEnabled) return;

  // 防重复处理检查
  if (_processingImages.contains(imageUrl)) {
    debugPrint('MultiBrowserPage: Image already being processed, skipping: $imageUrl');
    return;
  }

  // 检查最小处理间隔
  final lastProcessTime = _lastProcessTime[imageUrl];
  if (lastProcessTime != null) {
    final timeSinceLastProcess = DateTime.now().difference(lastProcessTime);
    if (timeSinceLastProcess < _minProcessInterval) {
      debugPrint('MultiBrowserPage: Image processed too recently (${timeSinceLastProcess.inMilliseconds}ms ago), skipping: $imageUrl');
      return;
    }
  }

  // 检查缓存
  final isCached = await _translationWorkflow.isImageCached(imageUrl);
  if (isCached) {
    debugPrint('MultiBrowserPage: Image already cached: $imageUrl');
    return;
  }

  // 记录开始处理
  _processingImages.add(imageUrl);
  _lastProcessTime[imageUrl] = DateTime.now();

  try {
    // 调用统一的处理方法
    await _processImageTranslation(imageUrl, context);
  } finally {
    // 确保处理完成后移除标记
    _processingImages.remove(imageUrl);
  }
}
```

#### **_processImageTranslation方法增强**
```dart
Future<void> _processImageTranslation(String imageUrl, BuildContext context) async {
  // 额外的防重复检查（针对手动调用）
  if (_processingImages.contains(imageUrl)) {
    debugPrint('MultiBrowserPage: Image already being processed in another call, skipping: $imageUrl');
    return;
  }

  try {
    // 标记为正在处理（如果还没有标记的话）
    _processingImages.add(imageUrl);
    _lastProcessTime[imageUrl] = DateTime.now();
    
    // 处理逻辑...
  } finally {
    // 确保在所有情况下都清理处理标记
    _processingImages.remove(imageUrl);
  }
}
```

### 2. JavaScript端防重复机制

#### **IntersectionObserver回调增强**
```javascript
// 增强的防重复检查
if (!this.imageCache.has(imageUrl) && !this.processingImages.has(imageUrl)) {
  // 额外检查：确保图片在状态映射中标记为可见
  const imageState = this.imageStateMap.get(imageUrl);
  const shouldProcess = !imageState || imageState.isVisible;
  
  if (shouldProcess) {
    console.log('主要内容图片进入视口，准备处理:', imageUrl);
    // 标记为正在处理，防止重复触发
    this.processingImages.add(imageUrl);
    
    // 触发处理事件
    if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
      window.flutter_inappwebview.callHandler('onImageVisible', imageUrl);
    }
    
    // 设置超时清理，防止处理标记永久保留
    setTimeout(() => {
      this.processingImages.delete(imageUrl);
    }, 30000); // 30秒后自动清理
  }
}
```

#### **图片切换检测增强**
```javascript
// 通知新图片可见（添加防重复检查）
imagesToUpdate.forEach(imageUrl => {
  console.log('检测到图片重新可见:', imageUrl);
  
  // 防重复检查：确保图片没有被缓存且没有正在处理
  if (!this.imageCache.has(imageUrl) && !this.processingImages.has(imageUrl)) {
    console.log('图片未缓存且未处理，通知Flutter端:', imageUrl);
    if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
      window.flutter_inappwebview.callHandler('onImageVisible', imageUrl);
    }
  } else {
    console.log('图片已缓存或正在处理，跳过通知:', imageUrl, {
      cached: this.imageCache.has(imageUrl),
      processing: this.processingImages.has(imageUrl)
    });
  }
});
```

### 3. 状态清理机制

#### **WebViewOverlayService新增方法**
```dart
/// 清理JavaScript端的处理状态（防重复处理）
Future<void> clearProcessingStates() async {
  const script = '''
    (function() {
      if (window.translationOverlays) {
        // 清理处理中的图片集合
        window.translationOverlays.processingImages.clear();
        console.log('已清理JavaScript端的图片处理状态');
        return true;
      }
      return false;
    })();
  ''';

  final success = await _executeJavaScriptSimple(script, operationName: 'clear processing states');
  if (success) {
    debugPrint('WebViewOverlayService: 已清理JavaScript端的处理状态');
  }
}
```

#### **TranslationWorkflowService新增方法**
```dart
/// Clear processing states to prevent duplicate processing
Future<void> clearProcessingStates() async {
  if (!_isInitialized) return;

  try {
    await _overlayService.clearProcessingStates();
    debugPrint('TranslationWorkflowService: Processing states cleared');
  } catch (e) {
    debugPrint('TranslationWorkflowService: Failed to clear processing states - $e');
  }
}
```

#### **MultiBrowserPage清理增强**
```dart
// 清理翻译状态（用于标签页切换和页面导航）
void _cleanupTranslationState() {
  if (_translationToggleEnabled) {
    _translationWorkflow.cleanup().catchError((e) {
      debugPrint('Error cleaning up translation state: $e');
    });
    _translationToggleEnabled = false;
  }
  
  // 清理防重复处理状态
  _processingImages.clear();
  _lastProcessTime.clear();
  
  // 清理JavaScript端的处理状态
  _translationWorkflow.clearProcessingStates().catchError((e) {
    debugPrint('Error clearing JavaScript processing states: $e');
  });
}
```

## 防重复机制层次

### 第一层：时间间隔检查
- **最小处理间隔**：1秒内不重复处理同一图片
- **适用场景**：快速连续的事件触发

### 第二层：处理状态检查
- **处理中集合**：维护正在处理的图片URL集合
- **适用场景**：并发处理请求

### 第三层：缓存状态检查
- **缓存检查**：已缓存的图片不再处理
- **适用场景**：避免重复处理已完成的图片

### 第四层：JavaScript端状态同步
- **状态映射**：JavaScript端维护图片可见性状态
- **超时清理**：30秒自动清理处理标记
- **适用场景**：防止JavaScript端状态永久保留

## 预期效果

### 改进前
```
flutter: MultiBrowserPage: Image became visible: blob:https://mangadx.org/xxx
flutter: MultiBrowserPage: Processing translation request for: blob:https://mangadx.org/xxx
flutter: MultiBrowserPage: Image became visible: blob:https://mangadx.org/xxx  // 重复
flutter: MultiBrowserPage: Processing translation request for: blob:https://mangadx.org/xxx  // 重复
```

### 改进后
```
flutter: MultiBrowserPage: Image became visible: blob:https://mangadx.org/xxx
flutter: MultiBrowserPage: Processing translation request for: blob:https://mangadx.org/xxx
flutter: MultiBrowserPage: Image already being processed, skipping: blob:https://mangadx.org/xxx  // 被阻止
```

## 使用场景

1. **MangaDX翻页**：防止图片切换时的重复处理
2. **快速滚动**：防止快速滚动时的重复触发
3. **页面刷新**：防止页面重新加载时的重复处理
4. **标签切换**：防止标签切换时的状态混乱

这套防重复处理机制通过多层检查和状态同步，有效解决了图片翻译过程中的重复处理问题，提高了系统效率和用户体验。

# Font Size Optimization for Translation Overlays

## Overview

The WebViewOverlayService has been enhanced with intelligent font size calculation that optimizes text readability while respecting OCR-detected bounding boxes. The new system automatically adjusts font sizes and expands regions when necessary to ensure optimal text display.

## Key Features

### 1. **Intelligent Text Fitting**
- Analyzes translated text length and estimates required space
- Considers both horizontal (width) and vertical (height) constraints
- Accounts for text wrapping when text exceeds region width
- Uses realistic character width and line height ratios

### 2. **Minimum Readability Threshold**
- Enforces minimum font size (default: 12px) for readability
- Automatically triggers region expansion when text would be too small
- Prevents unreadable micro-text in small OCR regions

### 3. **Smart Region Expansion**
- **Width-first strategy**: Expands width before height when possible
- **Multi-line support**: Calculates optimal line count for expanded regions
- **Expansion limits**: Maximum 150% of original dimensions by default
- **Fallback handling**: Graceful degradation when expansion isn't sufficient

### 4. **Comprehensive Logging**
- Detailed debug output for font size calculations
- Tracks expansion decisions and reasoning
- Monitors original vs. adjusted dimensions

## Implementation Details

### Core Classes

#### `FontSizeCalculationResult`
```dart
class FontSizeCalculationResult {
  final double fontSize;           // Calculated optimal font size
  final double adjustedWidth;      // Potentially expanded width
  final double adjustedHeight;     // Potentially expanded height
  final bool wasExpanded;          // Whether region was expanded
  final String? expansionReason;   // Explanation of expansion logic
}
```

### Key Methods

#### `_calculateOptimalFontSize()`
**Primary method for font size calculation**

```dart
FontSizeCalculationResult _calculateOptimalFontSize({
  required String translatedText,
  required double originalWidth,
  required double originalHeight,
  double minFontSize = 12.0,
  double maxFontSize = 24.0,
  double maxExpansionFactor = 1.5,
})
```

**Parameters:**
- `translatedText`: The actual translated text to be displayed
- `originalWidth/Height`: OCR-detected bounding box dimensions
- `minFontSize`: Minimum readable font size (default: 12px)
- `maxFontSize`: Maximum font size limit (default: 24px)
- `maxExpansionFactor`: Maximum region expansion (default: 1.5x)

**Algorithm:**
1. Estimates character dimensions using font size ratios
2. Calculates required lines based on text length and available width
3. Determines font size constraints from both width and height
4. Uses the more restrictive constraint as the base font size
5. Triggers expansion if font size falls below minimum threshold

#### `_expandRegionForReadability()`
**Handles region expansion when font size is too small**

**Expansion Strategy:**
1. **Single Line Expansion**: If text can fit in one line with width expansion
2. **Multi-Line Expansion**: Expands both width and height for multiple lines
3. **Line Limiting**: Caps at maximum 5 lines to prevent excessive height
4. **Proportional Scaling**: Maintains aspect ratio when possible

#### `_estimateLineCount()`
**Estimates text wrapping behavior**

- Simulates word wrapping based on character width
- Accounts for spaces between words
- Provides realistic line count estimates for font size calculation

### Integration Points

#### Updated Overlay Creation
The main overlay creation process now uses the optimized font size calculation:

```dart
// Old approach
final fontSize = _calculateFontSize(boundingBox.width, boundingBox.height);

// New approach
final fontSizeResult = _calculateOptimalFontSize(
  translatedText: element.text,
  originalWidth: boundingBox.width,
  originalHeight: boundingBox.height,
);
```

#### JavaScript Integration
The calculated dimensions are passed to the JavaScript overlay creation:

```javascript
window.translationOverlays.createImageOverlay(
  overlayId,
  translatedText,
  imageUrl,
  x, y,
  fontSizeResult.adjustedWidth,    // Uses expanded width
  fontSizeResult.adjustedHeight,   // Uses expanded height
  fontSizeResult.fontSize          // Optimized font size
);
```

#### Fallback Support
Enhanced `_createSimpleOverlay()` method accepts adjusted dimensions:

```dart
await _createSimpleOverlay(
  overlayId, 
  text, 
  x, y, 
  fontSize,
  width: adjustedWidth,
  height: adjustedHeight
);
```

## Configuration Options

### Default Parameters
- **Minimum Font Size**: 12px (ensures readability)
- **Maximum Font Size**: 24px (prevents oversized text)
- **Maximum Expansion**: 1.5x original dimensions
- **Character Width Ratio**: 0.6 (60% of font size)
- **Line Height Ratio**: 1.2 (120% of font size)

### Customization
Parameters can be adjusted based on specific requirements:

```dart
final result = _calculateOptimalFontSize(
  translatedText: text,
  originalWidth: width,
  originalHeight: height,
  minFontSize: 10.0,        // Smaller minimum for dense layouts
  maxFontSize: 20.0,        // Smaller maximum for consistency
  maxExpansionFactor: 2.0,  // Allow more expansion if needed
);
```

## Benefits

### 1. **Improved Readability**
- Ensures text is never too small to read
- Maintains consistent minimum font sizes across all overlays
- Adapts to different text lengths and content types

### 2. **Better Space Utilization**
- Maximizes use of available OCR region space
- Intelligently expands regions only when necessary
- Balances text size with layout preservation

### 3. **Enhanced User Experience**
- Consistent text appearance across different image types
- Reduced eye strain from micro-text
- Better text-to-background contrast

### 4. **Robust Error Handling**
- Graceful fallbacks when calculations fail
- Comprehensive logging for debugging
- Maintains functionality even with edge cases

## Debug Output Example

```
WebViewOverlayService: Calculating font size for text: "这是一个很长的翻译文本示例..."
WebViewOverlayService: Original dimensions: 120.0x30.0
WebViewOverlayService: Text length: 45, estimated lines: 2
WebViewOverlayService: Font size by height: 12.5, by width: 8.0
WebViewOverlayService: Initial calculated font size: 8.0
WebViewOverlayService: Font size below minimum, applying region expansion
WebViewOverlayService: Width expanded to 180.0 for single line
WebViewOverlayService: Final font size after expansion: 12.0
WebViewOverlayService: Font size calculation result: FontSizeCalculationResult(fontSize: 12.0, adjustedSize: 180.0x30.0, wasExpanded: true, reason: width expanded for single line)
```

## Future Enhancements

1. **Dynamic Character Width**: Adjust ratios based on actual font families
2. **Language-Specific Optimization**: Different ratios for different languages
3. **Viewport Awareness**: Consider screen boundaries for expansion limits
4. **Performance Optimization**: Cache calculations for similar text patterns
5. **User Preferences**: Allow users to adjust minimum font size preferences

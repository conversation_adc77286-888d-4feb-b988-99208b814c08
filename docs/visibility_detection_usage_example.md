# 图片可见性检测使用示例

## 基本使用流程

### 1. 初始化和启动监控

```dart
class BrowserPage extends StatefulWidget {
  @override
  _BrowserPageState createState() => _BrowserPageState();
}

class _BrowserPageState extends State<BrowserPage> {
  final WebViewOverlayService _overlayService = WebViewOverlayService();
  InAppWebViewController? _webViewController;

  @override
  void initState() {
    super.initState();
    _initializeOverlayService();
  }

  Future<void> _initializeOverlayService() async {
    try {
      // 初始化overlay服务
      await _overlayService.initialize();
      
      // 启动图片可见性监控
      await _overlayService.startVisibilityMonitoring();
      
      debugPrint('图片可见性监控已启动');
    } catch (e) {
      debugPrint('初始化overlay服务失败: $e');
    }
  }

  @override
  void dispose() {
    // 清理资源，包括停止可见性监控
    _overlayService.cleanup();
    super.dispose();
  }
}
```

### 2. WebView配置和事件处理

```dart
InAppWebView(
  onWebViewCreated: (controller) async {
    _webViewController = controller;
    
    // 设置WebView控制器到overlay服务
    await _overlayService.setWebViewController(controller);
    
    // 添加JavaScript处理器来接收图片隐藏事件
    controller.addJavaScriptHandler(
      handlerName: 'onImageHidden',
      callback: (args) async {
        if (args.isNotEmpty) {
          final imageUrl = args[0] as String;
          await _overlayService.handleImageHidden(imageUrl);
        }
      },
    );
    
    // 添加图片可见事件处理器
    controller.addJavaScriptHandler(
      handlerName: 'onImageVisible',
      callback: (args) async {
        if (args.isNotEmpty) {
          final imageUrl = args[0] as String;
          debugPrint('图片变为可见: $imageUrl');
          // 可以在这里重新初始化overlay功能
        }
      },
    );
  },
  
  onLoadStop: (controller, url) async {
    // 页面加载完成后重新初始化图片观察
    await _overlayService.reinitializeImageObservation();
    
    // 执行一次手动检查，清理可能已隐藏的图片
    await _overlayService.checkAndCleanHiddenImages();
  },
)
```

### 3. 手动触发可见性检查

```dart
class ImageVisibilityManager {
  final WebViewOverlayService _overlayService;
  
  ImageVisibilityManager(this._overlayService);
  
  /// 检查特定图片的可见性
  Future<bool> checkImageVisibility(String imageUrl) async {
    try {
      final isVisible = await _overlayService.isImageStillVisible(imageUrl);
      debugPrint('图片 $imageUrl 可见性: $isVisible');
      
      if (!isVisible) {
        // 如果图片不可见，执行清理
        await _overlayService.cleanupImageCompletely(imageUrl);
        debugPrint('已清理隐藏图片的相关内容: $imageUrl');
      }
      
      return isVisible;
    } catch (e) {
      debugPrint('检查图片可见性失败: $e');
      return false;
    }
  }
  
  /// 批量检查所有图片的可见性
  Future<void> checkAllImagesVisibility() async {
    try {
      // 获取当前页面的所有内容图片
      final imageUrls = await _overlayService.getContentImageUrls();
      
      debugPrint('开始检查 ${imageUrls.length} 个图片的可见性');
      
      int hiddenCount = 0;
      for (final imageUrl in imageUrls) {
        final isVisible = await checkImageVisibility(imageUrl);
        if (!isVisible) {
          hiddenCount++;
        }
      }
      
      debugPrint('检查完成，发现 $hiddenCount 个隐藏图片');
    } catch (e) {
      debugPrint('批量检查图片可见性失败: $e');
    }
  }
  
  /// 强制清理指定图片的所有内容
  Future<void> forceCleanupImage(String imageUrl) async {
    try {
      await _overlayService.cleanupImageCompletely(imageUrl);
      debugPrint('已强制清理图片内容: $imageUrl');
    } catch (e) {
      debugPrint('强制清理图片失败: $e');
    }
  }
}
```

### 4. 定期维护和监控

```dart
class OverlayMaintenanceService {
  final WebViewOverlayService _overlayService;
  Timer? _maintenanceTimer;
  
  OverlayMaintenanceService(this._overlayService);
  
  /// 启动定期维护
  void startMaintenance() {
    // 每30秒执行一次维护检查
    _maintenanceTimer = Timer.periodic(Duration(seconds: 30), (timer) {
      _performMaintenance();
    });
    
    debugPrint('Overlay维护服务已启动');
  }
  
  /// 停止定期维护
  void stopMaintenance() {
    _maintenanceTimer?.cancel();
    _maintenanceTimer = null;
    debugPrint('Overlay维护服务已停止');
  }
  
  /// 执行维护任务
  Future<void> _performMaintenance() async {
    try {
      debugPrint('开始执行overlay维护任务');
      
      // 1. 检查并清理隐藏的图片
      await _overlayService.checkAndCleanHiddenImages();
      
      // 2. 清理所有加载指示器（防止卡住）
      await _overlayService.hideAllLoadingIndicators();
      
      // 3. 清理所有缓存指示器
      await _overlayService.hideAllCacheIndicators();
      
      debugPrint('Overlay维护任务完成');
    } catch (e) {
      debugPrint('执行维护任务时发生错误: $e');
    }
  }
}
```

### 5. 测试和调试

```dart
class OverlayTestingService {
  final WebViewOverlayService _overlayService;
  
  OverlayTestingService(this._overlayService);
  
  /// 运行完整的可见性检测测试
  Future<void> runVisibilityTest() async {
    debugPrint('=== 开始图片可见性检测测试 ===');
    
    try {
      // 运行内置测试
      await _overlayService.testVisibilityDetectionAndCleanup();
      
      // 额外的自定义测试
      await _runCustomVisibilityTests();
      
      debugPrint('=== 图片可见性检测测试完成 ===');
    } catch (e) {
      debugPrint('可见性检测测试失败: $e');
    }
  }
  
  /// 自定义可见性测试
  Future<void> _runCustomVisibilityTests() async {
    // 测试1：检查当前页面的图片
    final imageUrls = await _overlayService.getContentImageUrls();
    debugPrint('当前页面图片数量: ${imageUrls.length}');
    
    // 测试2：逐个检查图片可见性
    for (int i = 0; i < imageUrls.length && i < 5; i++) {
      final imageUrl = imageUrls[i];
      final isVisible = await _overlayService.isImageStillVisible(imageUrl);
      debugPrint('图片 ${i + 1} 可见性: $isVisible');
    }
    
    // 测试3：测试清理功能
    const testImageUrl = 'test://example-image.jpg';
    await _overlayService.cleanupImageCompletely(testImageUrl);
    debugPrint('测试清理功能完成');
  }
  
  /// 模拟图片隐藏场景
  Future<void> simulateImageHiding() async {
    debugPrint('模拟图片隐藏场景');
    
    // 通过JavaScript隐藏第一个图片
    const script = '''
      (function() {
        const images = document.querySelectorAll('img');
        if (images.length > 0) {
          const firstImage = images[0];
          firstImage.style.display = 'none';
          console.log('已隐藏第一个图片:', firstImage.src);
          return firstImage.src;
        }
        return null;
      })();
    ''';
    
    // 执行隐藏脚本
    // final hiddenImageUrl = await _webViewController?.evaluateJavascript(source: script);
    
    // 等待2秒让监控系统检测到变化
    await Future.delayed(Duration(seconds: 2));
    
    // 手动触发检查
    await _overlayService.checkAndCleanHiddenImages();
    
    debugPrint('图片隐藏模拟完成');
  }
}
```

### 6. 错误处理和恢复

```dart
class OverlayErrorHandler {
  final WebViewOverlayService _overlayService;
  
  OverlayErrorHandler(this._overlayService);
  
  /// 处理overlay相关错误
  Future<void> handleOverlayError(String operation, dynamic error) async {
    debugPrint('Overlay操作错误 [$operation]: $error');
    
    try {
      switch (operation) {
        case 'visibility_check':
          // 可见性检查失败，重启监控
          await _restartVisibilityMonitoring();
          break;
          
        case 'cleanup':
          // 清理失败，尝试强制清理
          await _forceCleanupAll();
          break;
          
        case 'initialization':
          // 初始化失败，重新初始化
          await _reinitializeService();
          break;
          
        default:
          debugPrint('未知的overlay操作错误类型: $operation');
      }
    } catch (e) {
      debugPrint('处理overlay错误时发生异常: $e');
    }
  }
  
  /// 重启可见性监控
  Future<void> _restartVisibilityMonitoring() async {
    try {
      await _overlayService.stopVisibilityMonitoring();
      await Future.delayed(Duration(seconds: 1));
      await _overlayService.startVisibilityMonitoring();
      debugPrint('可见性监控已重启');
    } catch (e) {
      debugPrint('重启可见性监控失败: $e');
    }
  }
  
  /// 强制清理所有内容
  Future<void> _forceCleanupAll() async {
    try {
      await _overlayService.hideOverlays();
      await _overlayService.hideAllLoadingIndicators();
      await _overlayService.hideAllCacheIndicators();
      debugPrint('强制清理完成');
    } catch (e) {
      debugPrint('强制清理失败: $e');
    }
  }
  
  /// 重新初始化服务
  Future<void> _reinitializeService() async {
    try {
      await _overlayService.cleanup();
      await Future.delayed(Duration(seconds: 1));
      await _overlayService.initialize();
      await _overlayService.startVisibilityMonitoring();
      debugPrint('服务重新初始化完成');
    } catch (e) {
      debugPrint('重新初始化服务失败: $e');
    }
  }
}
```

## 完整使用示例

```dart
class CompleteBrowserExample extends StatefulWidget {
  @override
  _CompleteBrowserExampleState createState() => _CompleteBrowserExampleState();
}

class _CompleteBrowserExampleState extends State<CompleteBrowserExample> {
  final WebViewOverlayService _overlayService = WebViewOverlayService();
  late ImageVisibilityManager _visibilityManager;
  late OverlayMaintenanceService _maintenanceService;
  late OverlayTestingService _testingService;
  late OverlayErrorHandler _errorHandler;
  
  @override
  void initState() {
    super.initState();
    _initializeServices();
  }
  
  Future<void> _initializeServices() async {
    try {
      // 初始化overlay服务
      await _overlayService.initialize();
      
      // 初始化辅助服务
      _visibilityManager = ImageVisibilityManager(_overlayService);
      _maintenanceService = OverlayMaintenanceService(_overlayService);
      _testingService = OverlayTestingService(_overlayService);
      _errorHandler = OverlayErrorHandler(_overlayService);
      
      // 启动监控和维护
      await _overlayService.startVisibilityMonitoring();
      _maintenanceService.startMaintenance();
      
      debugPrint('所有服务初始化完成');
    } catch (e) {
      await _errorHandler.handleOverlayError('initialization', e);
    }
  }
  
  @override
  void dispose() {
    _maintenanceService.stopMaintenance();
    _overlayService.cleanup();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('图片可见性检测示例'),
        actions: [
          IconButton(
            icon: Icon(Icons.visibility),
            onPressed: () => _visibilityManager.checkAllImagesVisibility(),
          ),
          IconButton(
            icon: Icon(Icons.bug_report),
            onPressed: () => _testingService.runVisibilityTest(),
          ),
        ],
      ),
      body: InAppWebView(
        // WebView配置...
        onWebViewCreated: (controller) async {
          await _overlayService.setWebViewController(controller);
          // 添加事件处理器...
        },
        onLoadStop: (controller, url) async {
          await _overlayService.reinitializeImageObservation();
          await _overlayService.checkAndCleanHiddenImages();
        },
      ),
    );
  }
}
```

这个完整的示例展示了如何在实际应用中集成和使用图片可见性检测功能，包括初始化、监控、维护、测试和错误处理等各个方面。

# 增强图片切换检测系统

## 概述

针对MangaDX等漫画网站的动态图片切换场景，对WebViewOverlayService进行了全面改进，实现了基于MutationObserver的实时检测机制，解决了原有2秒定时检查响应过慢的问题。

## 问题分析

### 原有方案的局限性
1. **响应延迟**：2秒定时检查导致用户切换图片后需要等待最多2秒才能清理overlay
2. **检测不准确**：无法区分真正的图片切换和临时的样式变化
3. **性能浪费**：定期全页面扫描消耗不必要的资源
4. **通用性不足**：没有针对特定网站的图片切换模式进行优化

### MangaDX等网站的图片切换特点
- 使用CSS `display: none/block` 控制图片显示/隐藏
- 图片DOM元素保留在页面中，不会被移除
- 切换速度快，用户期望overlay立即响应
- 通常在图片容器或父元素上应用样式变化

## 解决方案

### 1. 实时检测机制

#### **MutationObserver监控**
```javascript
// 初始化DOM变化监控器（实时检测图片切换）
initMutationObserver: function() {
  if (this.mutationObserver) {
    this.mutationObserver.disconnect();
  }

  this.mutationObserver = new MutationObserver((mutations) => {
    this.handleDOMChanges(mutations);
  });

  // 监控配置：关注属性变化（特别是style属性）和子节点变化
  const config = {
    attributes: true,
    attributeFilter: ['style', 'class', 'hidden'], // 监控样式和类名变化
    childList: true, // 监控子节点添加/删除
    subtree: true, // 监控整个子树
    attributeOldValue: true // 保留旧属性值用于比较
  };

  this.mutationObserver.observe(document.body, config);
  console.log('DOM变化监控器已启动，监控图片切换事件');
}
```

#### **图片状态跟踪**
```javascript
// 图片状态映射表结构
imageStateMap: new Map(), // URL -> { isVisible, lastChecked, hasOverlay, element }

// 初始化图片状态映射表
initImageStateMap: function() {
  this.imageStateMap.clear();
  
  // 记录当前所有图片的可见性状态
  const images = document.querySelectorAll('img');
  images.forEach(img => {
    if (this.isMainContentImage(img)) {
      const imageUrl = this.normalizeImageUrl(img.src);
      const isVisible = this.isImageVisuallyVisible(img);
      this.imageStateMap.set(imageUrl, {
        isVisible: isVisible,
        lastChecked: Date.now(),
        hasOverlay: this.hasImageOverlays(imageUrl),
        element: img
      });
    }
  });
  
  console.log(`已初始化 ${this.imageStateMap.size} 个图片的状态映射`);
}
```

### 2. 智能变化检测

#### **DOM变化处理**
```javascript
// 处理DOM变化事件
handleDOMChanges: function(mutations) {
  const now = Date.now();
  
  // 防抖：避免过于频繁的检查
  if (now - this.lastVisibilityCheck < 100) {
    return;
  }
  this.lastVisibilityCheck = now;

  let hasImageChanges = false;
  const changedImages = new Set();

  mutations.forEach(mutation => {
    // 处理属性变化（主要是style变化）
    if (mutation.type === 'attributes') {
      const target = mutation.target;
      
      // 检查是否是图片或包含图片的元素
      if (target.tagName === 'IMG' && this.isMainContentImage(target)) {
        changedImages.add(target);
        hasImageChanges = true;
      } else {
        // 检查子元素中的图片
        const childImages = target.querySelectorAll('img');
        childImages.forEach(img => {
          if (this.isMainContentImage(img)) {
            changedImages.add(img);
            hasImageChanges = true;
          }
        });
      }
    }
  });

  // 如果检测到图片相关变化，立即检查可见性
  if (hasImageChanges) {
    console.log(`检测到 ${changedImages.size} 个图片发生变化，立即检查可见性`);
    this.checkChangedImages(Array.from(changedImages));
  }
}
```

#### **变化图片检查**
```javascript
// 检查发生变化的图片
checkChangedImages: function(changedImages) {
  const imagesToClean = [];
  const imagesToUpdate = [];

  changedImages.forEach(img => {
    const imageUrl = this.normalizeImageUrl(img.src);
    const currentVisibility = this.isImageVisuallyVisible(img);
    const previousState = this.imageStateMap.get(imageUrl);

    if (previousState) {
      // 检查可见性是否发生变化
      if (previousState.isVisible !== currentVisibility) {
        console.log(`图片可见性发生变化: ${imageUrl}, ${previousState.isVisible} -> ${currentVisibility}`);
        
        if (!currentVisibility && previousState.hasOverlay) {
          // 图片从可见变为不可见，且有overlay，需要清理
          imagesToClean.push(imageUrl);
        } else if (currentVisibility && !previousState.isVisible) {
          // 图片从不可见变为可见，更新状态
          imagesToUpdate.push(imageUrl);
        }
      }
    }
  });

  // 执行清理操作
  imagesToClean.forEach(imageUrl => {
    console.log('实时检测到图片隐藏，立即清理:', imageUrl);
    this.cleanupImageCompletely(imageUrl);
  });
}
```

### 3. 多层检测机制

#### **三层检测策略**
1. **实时检测**：MutationObserver监控DOM变化，100ms内响应
2. **定期备用检查**：5秒间隔的状态映射检查，防止遗漏
3. **全面扫描**：15秒间隔的传统全页面检查，确保完整性

#### **性能优化**
- **防抖机制**：100ms内的重复变化只处理一次
- **增量检查**：只检查发生变化的图片，不是全页面扫描
- **状态缓存**：维护图片状态映射，避免重复计算
- **智能跳过**：跳过最近已检查的图片

### 4. Dart端集成

#### **增强的监控启动**
```dart
/// 启动增强的图片切换检测（专门针对MangaDX等网站）
Future<void> startEnhancedImageSwitchDetection() async {
  const script = '''
    (function() {
      // 专门针对漫画网站的图片切换检测
      if (window.translationOverlays) {
        window.translationOverlays.initVisibilityMonitoring();
        
        // 额外监控常见的图片容器变化
        const imageContainers = document.querySelectorAll('[class*="image"], [class*="page"], [id*="image"], [id*="page"]');
        console.log('找到', imageContainers.length, '个可能的图片容器，开始监控');
        
        return true;
      }
      return false;
    })();
  ''';
  
  final success = await _executeJavaScriptSimple(script, operationName: 'start enhanced image switch detection');
  if (success) {
    debugPrint('WebViewOverlayService: 增强图片切换检测已启动，专门优化漫画网站体验');
  }
}
```

#### **图片可见事件处理**
```dart
/// 处理图片可见事件的回调方法（新增）
/// 当JavaScript检测到图片重新可见时会调用此方法
Future<void> handleImageVisible(String imageUrl) async {
  debugPrint('WebViewOverlayService: 收到图片可见事件（实时检测）: $imageUrl');
  
  try {
    // 检查图片是否确实可见
    final isVisible = await isImageStillVisible(imageUrl);
    if (isVisible) {
      debugPrint('WebViewOverlayService: 确认图片重新可见，可以使用翻译功能: $imageUrl');
    }
  } catch (e) {
    debugPrint('WebViewOverlayService: 处理图片可见事件时发生错误: $imageUrl - $e');
  }
}
```

#### **网站类型检测**
```dart
/// 检查当前网站是否为已知的图片切换网站
Future<bool> isImageSwitchingSite() async {
  const script = '''
    (function() {
      const hostname = window.location.hostname.toLowerCase();
      const knownSites = [
        'mangadx.org',
        'mangakakalot.com',
        'manganelo.com',
        'mangafreak.net',
        'readmanga.live',
        'mangareader.net'
      ];
      
      return knownSites.some(site => hostname.includes(site));
    })();
  ''';

  // 检测逻辑...
}
```

## 技术特性

### 1. **实时响应**
- **100ms响应时间**：DOM变化后100ms内检测到图片切换
- **立即清理**：检测到图片隐藏后立即清理相关overlay
- **防抖优化**：避免频繁变化导致的性能问题

### 2. **智能检测**
- **状态比较**：只有真正的可见性变化才触发处理
- **增量更新**：只检查发生变化的图片
- **多层验证**：实时检测 + 定期备用 + 全面扫描

### 3. **网站适配**
- **通用检测**：适用于所有使用CSS控制图片显示的网站
- **特定优化**：针对MangaDX等知名漫画网站的特殊优化
- **容器监控**：监控图片容器的变化，不仅仅是图片本身

### 4. **错误处理**
- **异常隔离**：单个图片的检测错误不影响其他图片
- **降级处理**：实时检测失败时使用定期检查作为备用
- **状态恢复**：确保系统状态的一致性

## 使用方法

### 自动启动（推荐）
```dart
// 在reinitializeImageObservation中自动启动
await overlayService.reinitializeImageObservation();
```

### 手动启动增强检测
```dart
await overlayService.startEnhancedImageSwitchDetection();
```

### 检测网站类型
```dart
final isImageSwitchingSite = await overlayService.isImageSwitchingSite();
if (isImageSwitchingSite) {
  // 启用特殊优化
}
```

### 测试功能
```dart
await overlayService.testImageSwitchDetection();
```

## 预期效果

1. **即时响应**：用户切换图片后，上一张图片的overlay立即消失（<200ms）
2. **流畅体验**：新图片可以立即使用翻译功能，无需等待
3. **性能优化**：减少不必要的全页面扫描，提高系统效率
4. **通用兼容**：支持MangaDX、Mangakakalot等主流漫画网站

## 实际应用示例

### WebView配置
```dart
InAppWebView(
  onWebViewCreated: (controller) async {
    await _overlayService.setWebViewController(controller);

    // 添加图片可见性事件处理器
    controller.addJavaScriptHandler(
      handlerName: 'onImageVisible',
      callback: (args) async {
        if (args.isNotEmpty) {
          final imageUrl = args[0] as String;
          await _overlayService.handleImageVisible(imageUrl);
        }
      },
    );

    // 添加图片隐藏事件处理器
    controller.addJavaScriptHandler(
      handlerName: 'onImageHidden',
      callback: (args) async {
        if (args.isNotEmpty) {
          final imageUrl = args[0] as String;
          await _overlayService.handleImageHidden(imageUrl);
        }
      },
    );
  },

  onLoadStop: (controller, url) async {
    // 检查是否为图片切换网站
    final isImageSwitchingSite = await _overlayService.isImageSwitchingSite();

    if (isImageSwitchingSite) {
      // 启动增强检测
      await _overlayService.startEnhancedImageSwitchDetection();
      debugPrint('已为图片切换网站启用增强检测');
    } else {
      // 使用标准检测
      await _overlayService.startVisibilityMonitoring();
    }

    // 重新初始化图片观察
    await _overlayService.reinitializeImageObservation();
  },
)
```

### 调试和监控
```dart
// 获取当前监控状态
final stateMap = await _overlayService.getImageStateMap();
if (stateMap != null) {
  debugPrint('监控图片数量: ${stateMap['totalImages']}');
  debugPrint('监控状态: ${stateMap['monitoringActive'] ? '活跃' : '未激活'}');
}

// 运行测试
await _overlayService.testImageSwitchDetection();
```

这套增强的图片切换检测系统提供了实时、智能、高效的overlay管理机制，专门针对动态图片切换场景进行了优化，确保用户在浏览漫画等内容时获得流畅的翻译体验。

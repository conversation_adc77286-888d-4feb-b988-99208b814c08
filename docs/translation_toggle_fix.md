# 翻译开关首次启用问题修复

## 问题分析

### 问题现象
当第一次打开页面并点击翻译开关时，出现以下问题：
```
flutter: MultiBrowserPage: Image became visible: blob:https://mangadx.org/xxx
flutter: MultiBrowserPage: Image already being processed in another call, skipping: blob:https://mangadx.org/xxx
```

图片被错误地标记为"already being processed"，但实际上并没有开始处理。

### 根本原因

1. **初始化时序问题**：翻译开关状态设置和重新初始化的时序不正确
2. **防重复机制过于严格**：在`_processImageTranslation`中的额外检查阻止了正常处理
3. **状态清理不及时**：翻译开关启用时没有正确清理之前的处理状态

### 具体问题点

#### 1. 时序问题
```dart
// 原有问题代码
Future.delayed(const Duration(milliseconds: 1000), () async {
  if (_translationToggleEnabled && currentTab.controller != null) {  // 这里_translationToggleEnabled还是false
    await _translationWorkflow.reinitializeTranslationSystem();
  }
});

setState(() {
  _translationToggleEnabled = true;  // 这里才设置为true
});
```

#### 2. 过度防重复检查
```dart
// 原有问题代码
Future<void> _processImageTranslation(String imageUrl, BuildContext context) async {
  // 额外的防重复检查（针对手动调用）
  if (_processingImages.contains(imageUrl)) {
    debugPrint('MultiBrowserPage: Image already being processed in another call, skipping: $imageUrl');
    return;  // 这里阻止了正常处理
  }
}
```

## 解决方案

### 1. 修复初始化时序

#### **修复前**
```dart
// 注入浏览器增强功能
await _injectBrowserEnhancements(currentTab.controller!);

// 延迟执行重新初始化（此时_translationToggleEnabled还是false）
Future.delayed(const Duration(milliseconds: 1000), () async {
  if (_translationToggleEnabled && currentTab.controller != null) {
    await _translationWorkflow.reinitializeTranslationSystem();
  }
});

// 设置翻译开关状态
setState(() {
  _translationToggleEnabled = true;
});
```

#### **修复后**
```dart
// 注入浏览器增强功能
await _injectBrowserEnhancements(currentTab.controller!);

// 先设置翻译开关状态
setState(() {
  _translationToggleEnabled = true;
});

// 清理之前的处理状态，确保干净的开始
_processingImages.clear();
_lastProcessTime.clear();
await _translationWorkflow.clearProcessingStates();

// Enable translation mode (show action buttons)
final localizations = AppLocalizations.of(context)!;
await _translationWorkflow.setTranslationMode(true, buttonText: localizations.translateThisImage);

// 延迟执行重新初始化（此时_translationToggleEnabled已经是true）
Future.delayed(const Duration(milliseconds: 1000), () async {
  if (_translationToggleEnabled && currentTab.controller != null) {
    debugPrint('MultiBrowserPage: Reinitializing translation system after toggle enabled');
    await _translationWorkflow.reinitializeTranslationSystem();
  }
});
```

### 2. 简化防重复机制

#### **修复前**
```dart
Future<void> _processImageTranslation(String imageUrl, BuildContext context) async {
  // 额外的防重复检查（针对手动调用）
  if (_processingImages.contains(imageUrl)) {
    debugPrint('MultiBrowserPage: Image already being processed in another call, skipping: $imageUrl');
    return;
  }

  try {
    // 标记为正在处理（如果还没有标记的话）
    _processingImages.add(imageUrl);
    _lastProcessTime[imageUrl] = DateTime.now();
    
    // 处理逻辑...
  } finally {
    _processingImages.remove(imageUrl);
  }
}
```

#### **修复后**
```dart
Future<void> _processImageTranslation(String imageUrl, BuildContext context) async {
  if (!_servicesInitialized) {
    debugPrint('MultiBrowserPage: Services not initialized, skipping image processing');
    return;
  }

  try {
    debugPrint('MultiBrowserPage: Processing translation request for: $imageUrl');
    
    // 移除了额外的防重复检查，只在_processVisibleImage中进行检查
    // 处理逻辑...
  } catch (e) {
    // 错误处理...
  }
  // 移除了finally块中的处理标记清理
}
```

### 3. 改进JavaScript端检查逻辑

#### **新增shouldProcessImage方法**
```javascript
// 判断图片是否应该被处理（综合检查）
shouldProcessImage: function(imageUrl) {
  // 基本检查：缓存状态
  if (this.imageCache.has(imageUrl)) {
    return false;
  }

  // 检查是否正在处理（但允许一定的容错）
  if (this.processingImages.has(imageUrl)) {
    // 检查处理时间，如果超过30秒则认为可能卡住了，允许重新处理
    const now = Date.now();
    const imageState = this.imageStateMap.get(imageUrl);
    if (imageState && imageState.lastChecked && (now - imageState.lastChecked > 30000)) {
      console.log('图片处理可能卡住，允许重新处理:', imageUrl);
      this.processingImages.delete(imageUrl);
      return true;
    }
    return false;
  }

  // 检查图片可见性状态
  const imageState = this.imageStateMap.get(imageUrl);
  if (imageState && !imageState.isVisible) {
    return false;
  }

  return true;
}
```

#### **简化IntersectionObserver回调**
```javascript
// 修复前：复杂的防重复检查
if (!this.imageCache.has(imageUrl) && !this.processingImages.has(imageUrl)) {
  // 额外检查和标记逻辑...
  this.processingImages.add(imageUrl);
  // 触发处理...
  setTimeout(() => {
    this.processingImages.delete(imageUrl);
  }, 30000);
}

// 修复后：使用统一的检查逻辑
const shouldProcess = this.shouldProcessImage(imageUrl);

if (shouldProcess) {
  console.log('主要内容图片进入视口，准备处理:', imageUrl);
  
  // 触发处理事件
  if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
    window.flutter_inappwebview.callHandler('onImageVisible', imageUrl);
  }
}
```

### 4. 状态清理机制

#### **翻译开关启用时的状态清理**
```dart
// 清理之前的处理状态，确保干净的开始
_processingImages.clear();
_lastProcessTime.clear();
await _translationWorkflow.clearProcessingStates();
```

#### **JavaScript端状态清理**
```javascript
// 清理处理中的图片集合
window.translationOverlays.processingImages.clear();
console.log('已清理JavaScript端的图片处理状态');
```

## 防重复机制层次调整

### 修复前（过于严格）
1. **Dart端_processVisibleImage**：时间间隔 + 处理状态检查
2. **Dart端_processImageTranslation**：额外的处理状态检查（问题所在）
3. **JavaScript端IntersectionObserver**：缓存 + 处理状态检查 + 自动标记

### 修复后（合理平衡）
1. **Dart端_processVisibleImage**：时间间隔 + 处理状态检查 + 缓存检查
2. **Dart端_processImageTranslation**：仅基本检查，不重复防重复逻辑
3. **JavaScript端shouldProcessImage**：统一的综合检查，包含容错机制

## 预期效果

### 修复前（问题场景）
```
1. 用户点击翻译开关
2. 图片可见事件触发
3. 被错误标记为"already being processed"
4. 实际没有开始处理
```

### 修复后（正常流程）
```
1. 用户点击翻译开关
2. 清理之前的处理状态
3. 正确设置翻译开关状态
4. 重新初始化翻译系统
5. 图片可见事件触发
6. 正常开始处理翻译
```

## 测试验证

### 测试步骤
1. 打开MangaDX页面
2. 点击翻译开关
3. 观察日志输出

### 期望结果
```
flutter: MultiBrowserPage: Reinitializing translation system after toggle enabled
flutter: MultiBrowserPage: Image became visible: blob:https://mangadx.org/xxx
flutter: MultiBrowserPage: Processing translation request for: blob:https://mangadx.org/xxx
flutter: Translation progress for blob:https://mangadx.org/xxx: Checking cache for image...
```

### 不应该出现
```
flutter: MultiBrowserPage: Image already being processed in another call, skipping: xxx
```

这套修复方案解决了翻译开关首次启用时的处理阻塞问题，确保用户点击翻译开关后能够正常开始图片翻译处理。

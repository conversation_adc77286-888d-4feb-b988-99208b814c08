# WebView JavaScript 资源管理系统迁移文档

## 概述

本次迁移将原本嵌入在 Dart 代码中的 JavaScript 和 CSS 代码提取到独立的资源文件中，并创建了一个统一的资源管理系统。

## 迁移内容

### 1. 新增文件

#### 1.1 资源管理类
- `lib/services/webview_js_resources.dart` - JavaScript 资源管理器
- `lib/services/webview_injection_service.dart` - WebView 注入服务

#### 1.2 JavaScript 资源文件
- `assets/js/translation_overlay.js` - 翻译覆盖层核心功能
- `assets/js/translation_overlay_styles.css` - 翻译覆盖层样式
- `assets/js/browser_utils.js` - 浏览器通用工具库
- `assets/js/ad_block.js` - 广告拦截功能
- `assets/js/content_extractor.js` - 内容提取功能

#### 1.3 模板文件
- `assets/js/templates/css_injection.js` - CSS 注入模板（使用 `__PLACEHOLDER__` 格式）
- `assets/js/templates/js_injection.js` - JavaScript 注入模板（使用 `__PLACEHOLDER__` 格式）

### 2. 修改的文件

#### 2.1 配置文件
- `pubspec.yaml` - 添加 JavaScript 资源路径

#### 2.2 服务文件
- `lib/services/webview_overlay_service.dart` - 使用新的资源管理系统

#### 2.3 页面文件
- `lib/pages/browser/multi_browser_page.dart` - 集成新的注入服务

## 系统特性

### 1. 资源管理器 (WebViewJsResources)

#### 功能特性
- **缓存管理**: 自动缓存资源，支持过期时间和大小限制
- **依赖管理**: 支持资源依赖关系，按正确顺序加载
- **动态配置**: 支持运行时添加/移除资源路径
- **批量操作**: 支持批量加载和注入资源

#### 主要方法
```dart
// 获取资源内容
WebViewJsResources.getContent('resource_name')

// 按依赖顺序加载
WebViewJsResources.loadResourcesWithDependencies(
  ['browser_utils', 'ad_block'],
  injectFunction
)

// 缓存管理
WebViewJsResources.clearCache('resource_name')
WebViewJsResources.getCacheStats()
```

### 2. 注入服务 (WebViewInjectionService)

#### 功能特性
- **模板化注入**: 使用模板文件进行 CSS 和 JavaScript 注入
- **占位符系统**: 使用 `__PLACEHOLDER__` 格式避免 JavaScript 语法错误
- **重复注入防护**: 避免重复注入相同资源
- **WebView 状态检查**: 确保 WebView 准备就绪后再注入
- **批量注入**: 支持同时注入多个资源

#### 主要方法
```dart
// 注入 CSS
injectionService.injectCss(controller, cssContent, styleId: 'custom-styles')

// 注入 JavaScript
injectionService.injectJavaScript(controller, jsContent, namespace: 'customScripts')

// 检查 WebView 状态
injectionService.waitForWebViewReady(controller)
```

## 使用示例

### 1. 基本使用

```dart
// 获取资源内容
final jsContent = await WebViewJsResources.getContent('translation_overlay');

// 注入到 WebView
final injectionService = WebViewInjectionService();
await injectionService.injectJavaScript(controller, jsContent);
```

### 2. 按依赖顺序加载

```dart
// 自动处理依赖关系
final loadedResources = await WebViewJsResources.loadResourcesWithDependencies(
  ['browser_utils', 'ad_block', 'content_extractor'],
  (script) async {
    await injectionService.injectJavaScript(controller, script);
  },
);
```

### 3. 浏览器增强功能

```dart
// 注入浏览器增强功能
Future<void> injectBrowserEnhancements(InAppWebViewController controller) async {
  final isReady = await injectionService.waitForWebViewReady(controller);
  if (!isReady) return;

  final loadedResources = await WebViewJsResources.loadResourcesWithDependencies(
    ['browser_utils', 'ad_block', 'content_extractor'],
    (script) async {
      await injectionService.injectJavaScript(controller, script);
    },
  );

  // 初始化广告拦截
  await controller.evaluateJavascript(source: 'window.adBlocker.init();');
}
```

## 迁移优势

### 1. 代码组织
- **分离关注点**: JavaScript/CSS 代码与 Dart 代码分离
- **模块化**: 每个功能模块独立管理
- **可维护性**: 更容易维护和更新 JavaScript 代码

### 2. 性能优化
- **缓存机制**: 避免重复加载相同资源
- **按需加载**: 只加载需要的资源
- **依赖管理**: 确保资源按正确顺序加载

### 3. 扩展性
- **动态配置**: 支持运行时添加新资源
- **模板系统**: 支持自定义注入模板
- **插件化**: 易于添加新的浏览器功能

### 4. 开发体验
- **调试友好**: JavaScript 代码可以独立调试
- **版本控制**: 更好的代码版本管理
- **团队协作**: 前端开发者可以独立工作

## 兼容性

### 1. 向后兼容
- 所有现有功能保持不变
- API 接口保持兼容
- 现有代码无需大幅修改

### 2. 渐进迁移
- 可以逐步迁移其他 JavaScript 代码
- 支持混合使用新旧系统
- 不影响现有功能

## 测试

### 1. 单元测试
- `test/services/webview_js_resources_test.dart` - 资源管理器测试
- `test/services/webview_injection_service_test.dart` - 注入服务测试
- 覆盖主要功能和边界情况

### 2. 集成测试
- 验证与现有系统的集成
- 确保功能正常工作

## 后续计划

### 1. 功能扩展
- 添加更多浏览器增强功能
- 支持更多类型的资源文件
- 优化性能和内存使用

### 2. 工具支持
- 开发资源打包工具
- 添加资源压缩和优化
- 支持热重载开发模式

### 3. 文档完善
- 添加详细的 API 文档
- 提供更多使用示例
- 创建最佳实践指南 
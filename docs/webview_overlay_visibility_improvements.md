# WebView Overlay 可见性检测和清理改进

## 概述

针对动态隐藏图片的情况，对WebViewOverlayService进行了全面改进，实现了智能的图片可见性检测和同步清理机制。当网站通过CSS动态隐藏图片时，系统能够自动检测并清理相关的overlay内容。

## 问题分析

### 原有问题
1. **静态清理机制**：只能手动清理overlay，无法自动检测图片状态变化
2. **不完整的清理**：清理时可能遗漏某些相关元素（操作按钮、指示器等）
3. **性能浪费**：对已隐藏图片继续维护overlay和事件监听器
4. **用户体验问题**：隐藏图片的overlay仍然显示，造成界面混乱

### 常见隐藏方式
- `display: none` - 完全不显示元素
- `visibility: hidden` - 隐藏元素但保留空间
- `opacity: 0` - 透明显示
- 父元素隐藏导致子图片不可见

## 解决方案

### 1. JavaScript端改进

#### **新增图片可见性检测方法**
```javascript
// 检查图片是否在视觉上可见（考虑CSS样式）
isImageVisuallyVisible: function(img) {
  if (!img || !img.parentNode) {
    return false;
  }

  // 检查图片本身的可见性
  const imgStyle = window.getComputedStyle(img);
  if (imgStyle.display === 'none' || 
      imgStyle.visibility === 'hidden' || 
      imgStyle.opacity === '0') {
    return false;
  }

  // 检查父元素的可见性（递归向上检查）
  let parent = img.parentElement;
  while (parent && parent !== document.body) {
    const parentStyle = window.getComputedStyle(parent);
    if (parentStyle.display === 'none' || 
        parentStyle.visibility === 'hidden' || 
        parentStyle.opacity === '0') {
      return false;
    }
    parent = parent.parentElement;
  }

  // 检查图片是否在视口内（基本检查）
  const rect = img.getBoundingClientRect();
  const isInViewport = rect.width > 0 && rect.height > 0;

  return isInViewport;
}
```

#### **自动可见性监控系统**
```javascript
// 初始化图片可见性监控
initVisibilityMonitoring: function() {
  // 启动定期检查隐藏图片的定时器
  if (this.visibilityCheckInterval) {
    clearInterval(this.visibilityCheckInterval);
  }

  this.visibilityCheckInterval = setInterval(() => {
    this.checkAndCleanHiddenImages();
  }, 2000); // 每2秒检查一次

  console.log('图片可见性监控已启动');
}
```

#### **完整清理机制**
```javascript
// 完全清理图片的所有相关内容
cleanupImageCompletely: function(imageUrl) {
  try {
    // 1. 移除翻译overlay
    const overlaysRemoved = this.removeImageOverlays(imageUrl);
    
    // 2. 移除加载指示器
    this.hideLoadingIndicator(imageUrl);
    
    // 3. 移除缓存指示器
    this.hideCacheIndicator(imageUrl);
    
    // 4. 移除操作按钮
    this.removeActionButton(imageUrl);
    
    // 5. 清理缓存状态
    this.imageCache.delete(imageUrl);
    this.processingImages.delete(imageUrl);
    
    // 6. 通知Flutter端图片已隐藏
    if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
      window.flutter_inappwebview.callHandler('onImageHidden', imageUrl);
    }
    
    return true;
  } catch (error) {
    console.error('清理图片内容时发生错误:', imageUrl, error);
    return false;
  }
}
```

### 2. Dart端改进

#### **可见性监控控制方法**
```dart
/// 启动图片可见性监控
Future<void> startVisibilityMonitoring() async {
  const script = "window.translationOverlays.initVisibilityMonitoring();";
  
  final success = await _executeJavaScriptSimple(script, operationName: 'start visibility monitoring');
  if (success) {
    debugPrint('WebViewOverlayService: 图片可见性监控已启动');
  }
}

/// 停止图片可见性监控
Future<void> stopVisibilityMonitoring() async {
  const script = "window.translationOverlays.stopVisibilityMonitoring();";
  
  final success = await _executeJavaScriptSimple(script, operationName: 'stop visibility monitoring');
  if (success) {
    debugPrint('WebViewOverlayService: 图片可见性监控已停止');
  }
}
```

#### **图片状态检查方法**
```dart
/// 检查指定图片是否仍然可见
Future<bool> isImageStillVisible(String imageUrl) async {
  final script = '''
    (function() {
      return window.translationOverlays.isImageStillVisible('$imageUrl');
    })();
  ''';

  if (_currentController == null) return false;

  try {
    final result = await _currentController!.evaluateJavascript(source: script);
    return result == true;
  } catch (e) {
    debugPrint('WebViewOverlayService: 检查图片可见性失败 - $e');
    return false;
  }
}
```

#### **完整清理方法**
```dart
/// 完全清理指定图片的所有相关内容
Future<void> cleanupImageCompletely(String imageUrl) async {
  final script = '''
    (function() {
      return window.translationOverlays.cleanupImageCompletely('$imageUrl');
    })();
  ''';

  final success = await _executeJavaScriptSimple(script, operationName: 'cleanup image completely');
  if (success) {
    debugPrint('WebViewOverlayService: 已完全清理图片相关内容: $imageUrl');
    
    // 从活动overlay列表中移除相关ID
    _activeOverlayIds.removeWhere((id) => id.contains(imageUrl.hashCode.toString()));
  }
}
```

#### **事件处理方法**
```dart
/// 处理图片隐藏事件的回调方法
/// 当JavaScript检测到图片被隐藏时会调用此方法
Future<void> handleImageHidden(String imageUrl) async {
  debugPrint('WebViewOverlayService: 收到图片隐藏事件: $imageUrl');
  
  try {
    // 执行完全清理
    await cleanupImageCompletely(imageUrl);
    
    // 从活动overlay列表中移除相关ID
    _activeOverlayIds.removeWhere((id) => id.contains(imageUrl.hashCode.toString()));
    
    debugPrint('WebViewOverlayService: 已处理图片隐藏事件，完成清理: $imageUrl');
  } catch (e) {
    debugPrint('WebViewOverlayService: 处理图片隐藏事件时发生错误: $imageUrl - $e');
  }
}
```

### 3. 生命周期集成

#### **初始化时启动监控**
在`reinitializeImageObservation()`方法中自动启动可见性监控：
```dart
// 启动图片可见性监控
if (window.translationOverlays.initVisibilityMonitoring) {
  window.translationOverlays.initVisibilityMonitoring();
  console.log('图片可见性监控已启动');
}
```

#### **清理时停止监控**
在`cleanup()`方法中停止可见性监控：
```dart
// 停止图片可见性监控
await stopVisibilityMonitoring();
```

## 功能特性

### 1. **智能检测机制**
- **CSS样式检测**：检查`display`、`visibility`、`opacity`属性
- **父元素检查**：递归检查父元素的可见性状态
- **视口检查**：确认图片在视口内且有实际尺寸
- **定期监控**：每2秒自动检查一次所有相关图片

### 2. **完整清理策略**
- **翻译overlay**：移除所有相关的文本覆盖层
- **操作按钮**：清理浮动的翻译和下载按钮
- **指示器**：移除加载和缓存指示器
- **缓存状态**：清理内存中的图片状态信息
- **事件监听器**：移除相关的JavaScript事件处理

### 3. **性能优化**
- **避免重复处理**：对已隐藏图片不再创建overlay
- **及时清理**：立即释放不需要的资源
- **批量操作**：一次性清理多个隐藏图片
- **错误隔离**：单个图片的清理错误不影响其他图片

### 4. **错误处理**
- **异常捕获**：所有清理操作都有完整的错误处理
- **降级处理**：部分清理失败时继续处理其他部分
- **日志记录**：详细的中文调试信息
- **状态恢复**：确保系统状态的一致性

## 测试场景

### 1. **CSS动态隐藏**
```css
/* 测试场景1：display隐藏 */
.hidden-image { display: none; }

/* 测试场景2：visibility隐藏 */
.invisible-image { visibility: hidden; }

/* 测试场景3：透明隐藏 */
.transparent-image { opacity: 0; }

/* 测试场景4：父元素隐藏 */
.hidden-container { display: none; }
.hidden-container img { /* 子图片也会被隐藏 */ }
```

### 2. **动态显示/隐藏**
```javascript
// 动态隐藏图片
document.querySelector('#test-image').style.display = 'none';

// 动态显示图片
document.querySelector('#test-image').style.display = 'block';
```

### 3. **测试验证**
```dart
// 运行可见性检测测试
await overlayService.testVisibilityDetectionAndCleanup();
```

## 预期效果

1. **自动清理**：图片隐藏后，相关overlay立即消失
2. **性能提升**：不再对隐藏图片维护不必要的资源
3. **界面整洁**：避免显示无效的overlay和按钮
4. **用户体验**：图片重新显示后，overlay功能正常工作
5. **系统稳定**：清理过程中的异常不影响其他功能

## 使用方法

### 启动监控
```dart
await overlayService.startVisibilityMonitoring();
```

### 手动检查
```dart
await overlayService.checkAndCleanHiddenImages();
```

### 检查特定图片
```dart
final isVisible = await overlayService.isImageStillVisible(imageUrl);
```

### 完全清理
```dart
await overlayService.cleanupImageCompletely(imageUrl);
```

这套改进方案提供了完整的图片可见性检测和清理机制，确保overlay系统能够智能地适应动态变化的网页内容，提供更好的用户体验和系统性能。
